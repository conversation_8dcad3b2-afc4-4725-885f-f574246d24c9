name: Plugin Build

on:
  pull_request:
  push:
    branches:
      - "*"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup JDK
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Elevate wrapper permissions
        run: chmod +x ./gradlew

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          dependency-graph: generate-and-submit

      - name: Build Plugin
        run: ./gradlew build

      - name: Upload Plugin Artifact
        uses: actions/upload-artifact@v4
        with:
          name: plugin
          path: build/libs/*.jar
