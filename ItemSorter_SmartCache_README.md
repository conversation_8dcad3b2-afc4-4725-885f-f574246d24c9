# 智能物品分类模块 - 动态箱子缓存系统

## 系统特点

这是一个全新的智能物品分类系统，具有以下特点：

### 🔍 **动态箱子搜索**
- 实时搜索半径20格内的所有箱子
- 无需手动配置存储位置
- 自动发现和管理附近的存储容器

### 🧠 **智能缓存系统**
- 缓存箱子位置和内容物信息
- 自动识别箱子的专用物品类型
- 根据箱子内物品数量判断用途

### 📦 **自动箱子分配**
- 空箱子：可以放入任何物品，放入后成为该物品的专用箱子
- 专用箱子：只存储特定类型的物品
- 混合箱子：根据数量最多的物品类型确定用途

### 🎮 **生存模式优化**
- 不处理创造模式才能获得的物品（如刷怪蛋）
- 专为生存模式设计的物品分类系统

## 工作原理

### 1. 箱子发现和缓存
```
玩家周围20格 → 搜索所有箱子 → 缓存位置信息 → 定期更新缓存
```

### 2. 箱子内容分析
```
打开箱子 → 分析内容物 → 统计物品类型 → 确定专用类型 → 更新缓存
```

### 3. 智能存储分配
```
需要存储物品 → 查找专用箱子 → 找不到则找空箱子 → 存储物品 → 更新箱子用途
```

## 物品分类系统

### 🥕 单独物品分类（每种一个箱子）

#### 农作物和食物
- `gunpowder` - 火药
- `sugar_cane` - 甘蔗  
- `wheat` - 小麦
- `carrot` - 胡萝卜
- `potato` - 土豆（包括烤土豆、毒马铃薯）
- `beetroot` - 甜菜根
- `apple` - 苹果（包括金苹果、附魔金苹果）
- `bread` - 面包

#### 肉类
- `beef` - 牛肉（生熟一起）
- `porkchop` - 猪肉（生熟一起）
- `chicken` - 鸡肉（生熟一起）
- `mutton` - 羊肉（生熟一起）
- `rabbit` - 兔肉（生熟一起）
- `cod` - 鳕鱼（生熟一起）
- `salmon` - 鲑鱼（生熟一起）

#### 矿物资源
- `coal` - 煤炭（煤炭+木炭）
- `iron` - 铁（铁锭+粗铁+铁粒）
- `gold` - 金（金锭+粗金+金粒）
- `copper` - 铜（铜锭+粗铜）
- `diamond` - 钻石
- `emerald` - 绿宝石
- `redstone` - 红石
- `lapis_lazuli` - 青金石
- `quartz` - 石英
- `netherite` - 下界合金（锭+碎片+远古残骸）

#### 建筑材料
- `stone` - 石头（石头+圆石）
- `dirt` - 泥土（泥土+砂土+草方块）
- `sand` - 沙子（沙子+红沙）
- `gravel` - 沙砾
- `cobblestone` - 圆石（圆石+苔石圆石）

#### 原木和木板
- `oak_log` / `oak_planks` - 橡木
- `spruce_log` / `spruce_planks` - 云杉
- `birch_log` / `birch_planks` - 白桦
- `jungle_log` / `jungle_planks` - 丛林
- `acacia_log` / `acacia_planks` - 金合欢
- `dark_oak_log` / `dark_oak_planks` - 深色橡木

### 🎨 颜色变种合并（同种不同颜色放一起）

- `wool` - 羊毛（所有16种颜色）
- `concrete` - 混凝土（所有16种颜色）
- `terracotta` - 陶瓦（所有16种颜色）
- `glass` - 玻璃（普通+遮光+所有颜色染色玻璃）
- `carpet` - 地毯（所有16种颜色）
- `bed` - 床（所有16种颜色）

### ⭐ 特殊物品合并（生存模式可获得）

- `music_discs` - 音乐唱片（生存可获得的唱片）
- `pottery_sherds` - 陶片（考古获得的陶片）
- `ultra_rare` - 超稀有物品（鞘翅、图腾、下界之星、信标、潮涌核心、龙首）
- `enchanted_books` - 附魔书
- `potions` - 药水（所有类型药水）
- `trash` - 垃圾物品（低价值物品）

## 使用方法

### 基础设置
```bash
# 启用模块
itemsorter enable

# 添加源箱子（存放待分类物品的箱子）
itemsorter addchest 100 64 200
itemsorter addchest 105 64 200

# 查看模块状态
itemsorter status
```

### 箱子缓存管理
```bash
# 查看箱子缓存统计
itemsorter cachestats

# 清空箱子缓存（重新扫描）
itemsorter clearcache
```

### 自定义分类
```bash
# 添加自定义分类
itemsorter addcategory precious diamond emerald netherite_ingot

# 移除自定义分类
itemsorter removecategory precious

# 列出所有分类
itemsorter listcategories
```

## 箱子管理策略

### 🏗️ 仓库布局建议

#### 区域化布局
```
农作物区域：
[空箱子] [空箱子] [空箱子] [空箱子] [空箱子]
  ↓        ↓        ↓        ↓        ↓
 火药     甘蔗     小麦     胡萝卜    土豆

肉类区域：
[空箱子] [空箱子] [空箱子] [空箱子] [空箱子]
  ↓        ↓        ↓        ↓        ↓
 牛肉     猪肉     鸡肉     羊肉     兔肉

矿物区域：
[空箱子] [空箱子] [空箱子] [空箱子] [空箱子]
  ↓        ↓        ↓        ↓        ↓
 煤炭      铁       金      钻石     绿宝石
```

#### 智能分配过程
1. **第一次存储**：系统找到空箱子，放入火药，箱子变成"火药专用箱子"
2. **后续存储**：系统直接找到火药专用箱子，继续存储火药
3. **混合情况**：如果箱子里有多种物品，数量最多的物品类型决定箱子用途

### 📋 箱子状态说明

#### 空箱子 (Empty)
- 可以存储任何物品
- 存储后变成专用箱子
- 优先选择距离最近的空箱子

#### 专用箱子 (Dedicated)
- 只存储特定类型的物品
- 系统会优先使用专用箱子
- 显示为 "火药专用箱子" 等

#### 混合箱子 (Mixed)
- 包含多种物品类型
- 按数量最多的物品确定用途
- 例如：箱子里有50个火药和10个线，认为是"火药箱子"

## 配置参数

### 基础配置
```json
{
  "enabled": true,                    // 是否启用模块
  "chestSearchRadius": 20,            // 箱子搜索半径
  "processingIntervalTicks": 60,      // 处理间隔（3秒）
  "chestCacheUpdateInterval": 200,    // 缓存更新间隔（10秒）
  "enableSmartClassification": true,  // 智能分类
  "processCreativeItems": false,      // 不处理创造物品
  "defaultCategory": "misc"           // 默认分类
}
```

## 工作流程

### 1. 启动阶段
```
启用模块 → 搜索附近箱子 → 建立缓存 → 开始监控
```

### 2. 物品处理阶段
```
从源箱子提取物品 → 分类物品 → 查找目标箱子 → 存储物品 → 更新缓存
```

### 3. 缓存维护阶段
```
定期扫描 → 更新箱子状态 → 清理过期缓存 → 优化存储分配
```

## 优势特点

### ✅ 优点
1. **零配置** - 无需手动设置存储位置
2. **智能化** - 自动学习和适应箱子用途
3. **高效率** - 缓存系统减少重复搜索
4. **生存友好** - 专为生存模式优化
5. **自适应** - 根据实际使用情况调整

### ⚠️ 注意事项
1. **搜索范围** - 只处理20格内的箱子
2. **箱子数量** - 需要足够的空箱子供系统分配
3. **物品混合** - 避免手动在箱子里混合不同类型物品
4. **缓存更新** - 手动整理箱子后建议清空缓存

## 故障排除

### 常见问题

#### 找不到合适的箱子
- **原因**：附近没有空箱子或专用箱子已满
- **解决**：在20格内放置更多空箱子

#### 物品分类错误
- **原因**：自定义分类配置问题
- **解决**：检查自定义分类设置，使用 `itemsorter listcategories`

#### 缓存不准确
- **原因**：手动整理箱子后缓存未更新
- **解决**：使用 `itemsorter clearcache` 清空缓存

#### 系统不工作
- **原因**：模块未启用或配置错误
- **解决**：检查 `itemsorter status` 确认状态

这个智能缓存系统让物品分类变得完全自动化，你只需要在附近放置足够的空箱子，系统会自动学习和分配每个箱子的用途！
