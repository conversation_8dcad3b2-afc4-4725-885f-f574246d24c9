# 完整物品分类系统

基于ZenithProxy ItemRegistry.java中的所有物品ID，按照以下原则进行分类：
- 颜色不同的同种物品当成一种（如所有颜色的羊毛归为装饰建筑材料）
- 大宗物品（生产和消耗量特别多）单独算一种（如石头、泥土、原木等）
- 独特物品单独算一组（如鞘翅、烟花、图腾、刷怪蛋等）

## 分类系统概览

### 1. 工具类 (tools)
**用途**: 日常使用的各种工具
- 各种材质的铲子、镐、斧、锄
- 钓鱼竿、打火石、剪刀、指南针、时钟、望远镜
- 刷子、恢复指南针、胡萝卜钓竿、诡异菌钓竿

### 2. 武器装备类 (weapons_armor)
**用途**: 战斗和防护装备
- 各种材质的剑（木剑、石剑、铁剑、金剑、钻石剑、下界合金剑）
- 远程武器（弓、弩、三叉戟）
- 各种材质的盔甲（皮革、锁链、铁、金、钻石、下界合金）
- 盾牌、海龟壳

### 3. 大宗建筑材料类 (bulk_building)
**用途**: 大量使用的基础建筑材料
- **基础石材**: 石头、圆石、深板岩、花岗岩、闪长岩、安山岩、凝灰岩、方解石
- **泥土类**: 泥土、砂土、草方块、灰化土、缠根泥土、泥巴、粘土
- **沙石类**: 沙子、红沙、砂岩、红砂岩、沙砾
- **原木和木板**: 所有种类的原木和对应木板
- **下界材料**: 下界岩、灵魂沙、灵魂土、菌岩、下界砖
- **末地材料**: 末地石、紫珀块、末地石砖

### 4. 装饰建筑材料类 (decorative_blocks)
**用途**: 主要用于装饰的彩色方块
- **羊毛**: 所有16种颜色的羊毛
- **混凝土**: 所有16种颜色的混凝土
- **陶瓦**: 所有16种颜色的陶瓦和带釉陶瓦
- **玻璃**: 普通玻璃、遮光玻璃、所有颜色的染色玻璃
- **建筑构件**: 楼梯、台阶、栅栏、墙、门、活板门

### 5. 食物类 (food)
**用途**: 恢复饥饿值的物品
- **基础食物**: 苹果、金苹果、附魔金苹果、面包、曲奇、蛋糕、南瓜派
- **肉类**: 各种生肉和熟肉（猪肉、牛肉、鸡肉、羊肉、兔肉）
- **鱼类**: 鳕鱼、鲑鱼、热带鱼、河豚及其熟制品
- **蔬菜水果**: 土豆、胡萝卜、甜菜根、西瓜片、浆果、紫颂果
- **汤类**: 蘑菇煲、兔肉煲、甜菜汤、迷之炖菜
- **其他**: 蜂蜜瓶、牛奶桶、蜘蛛眼、腐肉、毒马铃薯

### 6. 矿物资源类 (ores_minerals)
**用途**: 矿物和金属材料
- **基础矿物**: 煤炭、木炭、铁锭、粗铁、铜锭、粗铜、金锭、粗金
- **宝石**: 钻石、绿宝石、青金石、红石、石英
- **高级材料**: 下界合金锭、下界合金碎片、远古残骸
- **碎片**: 铁粒、金粒、紫水晶碎片、海晶碎片、海晶砂粒、回响碎片

### 7. 红石电路类 (redstone)
**用途**: 红石电路和机械装置
- **基础红石**: 红石粉、红石火把、红石块、红石灯
- **逻辑元件**: 中继器、比较器、侦测器、标靶
- **机械装置**: 活塞、粘性活塞、发射器、投掷器、漏斗
- **传感器**: 阳光传感器、绊线钩、潜声传感器、校频潜声传感器
- **开关**: 拉杆、各种按钮、各种压力板

### 8. 药水类 (potions)
**用途**: 药水和相关物品
- 药水、喷溅药水、滞留药水
- 玻璃瓶、龙息、经验瓶、不祥之瓶

### 9. 附魔书类 (enchanted_books)
**用途**: 附魔相关
- 附魔书

### 10. 独特物品类 (unique_items)
**用途**: 稀有、特殊或独一无二的物品
- **特殊装备**: 鞘翅、不死图腾、下界之星、龙蛋、龙首
- **烟花**: 烟花火箭、烟花之星
- **试炼相关**: 试炼钥匙、不祥试炼钥匙、试炼刷怪笼、宝库、沉重核心、狼牙棒、疾风弹、微风棒
- **陶片**: 所有种类的陶片
- **刷怪蛋**: 所有生物的刷怪蛋

### 11. 音乐唱片类 (music_discs)
**用途**: 音乐娱乐
- 所有音乐唱片（13、cat、blocks、chirp、creator等）

### 12. 垃圾类 (trash)
**用途**: 低价值或常见的废料物品
- **低价值物品**: 腐肉、蜘蛛眼、发酵蜘蛛眼、毒马铃薯
- **植物废料**: 枯萎的灌木、草、高草、海草、海带、粘土球
- **动物产品**: 骨头、火药、线、羽毛、皮革、兔子皮、史莱姆球、幻翼膜
- **种子**: 小麦种子、甜菜种子、西瓜种子、南瓜种子、火把花种子、瓶子草荚果

### 13. 杂项类 (misc)
**用途**: 未分类的其他物品
- 所有无法归入上述分类的物品

## 分类优先级

1. **用户自定义分类** - 最高优先级
2. **独特物品** - 稀有物品优先识别
3. **音乐唱片** - 特殊收藏品
4. **智能属性分类** - 基于物品属性自动分类
5. **默认分类** - 杂项类

## 稀有度评分系统

### 超稀有 (1000+)
- 龙蛋 (1000)

### 极稀有 (500-999)
- 鞘翅 (500)

### 非常稀有 (300-499)
- 不死图腾 (400)
- 下界之星 (350)
- 狼牙棒 (300)

### 稀有 (200-299)
- 沉重核心 (280)
- 试炼钥匙 (250)
- 陶片 (200)

### 较稀有 (100-199)
- 音乐唱片 (180)
- 刷怪蛋 (150)
- 下界合金物品 (+120)
- 附魔物品 (+100)

### 常见 (0-99)
- 钻石物品 (+100)
- 绿宝石物品 (+90)
- 金物品 (+70)
- 铁物品 (+50)
- 铜物品 (+30)
- 石制物品 (+20)
- 木制物品 (+10)

## 使用建议

### 存储配置建议
```bash
# 高价值物品使用潜影盒
itemsorter setstorage unique_items 100 64 200 shulker
itemsorter setstorage music_discs 105 64 200 shulker
itemsorter setstorage enchanted_books 110 64 200 shulker

# 常用物品使用箱子
itemsorter setstorage tools 115 64 200 chest
itemsorter setstorage weapons_armor 120 64 200 chest
itemsorter setstorage food 125 64 200 chest

# 大宗物品使用大容量存储
itemsorter setstorage bulk_building 130 64 200 auto
itemsorter setstorage decorative_blocks 135 64 200 auto

# 垃圾物品可以集中处理
itemsorter setstorage trash 140 64 200 auto
```

### 自定义分类示例
```bash
# 创建贵重物品分类
itemsorter addcategory precious diamond emerald netherite_ingot ancient_debris
itemsorter setstorage precious 150 64 200 shulker

# 创建农业物品分类
itemsorter addcategory farming wheat_seeds carrot potato beetroot_seeds
itemsorter setstorage farming 155 64 200 chest
```

这个分类系统涵盖了ItemRegistry.java中的所有物品，确保每个物品都能找到合适的分类，同时考虑了物品的用途、稀有度和使用频率。
