# 智能物品分类模块 - 缺省分类系统

## 核心特性

### 🔄 **缺省分类逻辑**
- **有配置分类** → 使用配置的分类
- **无配置分类** → 物品名称本身就是分类名，单独放一个箱子

### 📦 **一物一箱原则**
- 每种物品都有自己的专用箱子
- 系统自动为每种物品分配存储空间
- 无需手动配置大部分物品的存储位置

## 分类系统

### 🎯 **预配置分类组**（包含多个物品的分类）

#### 食物变种组
- `potato` → 土豆、烤土豆、毒马铃薯
- `apple` → 苹果、金苹果、附魔金苹果

#### 肉类组（生熟一起）
- `beef` → 生牛肉、熟牛肉
- `porkchop` → 生猪肉、熟猪肉
- `chicken` → 生鸡肉、熟鸡肉
- `mutton` → 生羊肉、熟羊肉
- `rabbit` → 生兔肉、熟兔肉
- `cod` → 生鳕鱼、熟鳕鱼
- `salmon` → 生鲑鱼、熟鲑鱼

#### 矿物组（相关物品一起）
- `coal` → 煤炭、木炭
- `iron` → 铁锭、粗铁、铁粒
- `gold` → 金锭、粗金、金粒
- `copper` → 铜锭、粗铜
- `netherite` → 下界合金锭、下界合金碎片、远古残骸

#### 建筑材料组
- `stone` → 石头、圆石
- `dirt` → 泥土、砂土、草方块
- `sand` → 沙子、红沙
- `cobblestone` → 圆石、苔石圆石

#### 原木组（包含去皮版本）
- `oak_log` → 橡木原木、去皮橡木原木
- `spruce_log` → 云杉原木、去皮云杉原木
- `birch_log` → 白桦原木、去皮白桦原木
- `jungle_log` → 丛林原木、去皮丛林原木
- `acacia_log` → 金合欢原木、去皮金合欢原木
- `dark_oak_log` → 深色橡木原木、去皮深色橡木原木

#### 颜色变种组
- `wool` → 所有16种颜色的羊毛
- `concrete` → 所有16种颜色的混凝土
- `terracotta` → 所有16种颜色的陶瓦
- `glass` → 普通玻璃、遮光玻璃、所有颜色染色玻璃
- `carpet` → 所有16种颜色的地毯
- `bed` → 所有16种颜色的床

#### 特殊物品组
- `music_discs` → 所有音乐唱片
- `pottery_sherds` → 所有陶片
- `ultra_rare` → 鞘翅、图腾、下界之星、信标、潮涌核心、龙首
- `enchanted_books` → 所有附魔书
- `potions` → 所有药水
- `trash` → 垃圾物品

### 🔧 **缺省分类**（物品自己作为分类名）

所有没有在上述分类组中的物品，都会使用物品名称本身作为分类名：

#### 示例
- `gunpowder` → 火药箱子
- `sugar_cane` → 甘蔗箱子
- `wheat` → 小麦箱子
- `carrot` → 胡萝卜箱子
- `beetroot` → 甜菜根箱子
- `bread` → 面包箱子
- `diamond` → 钻石箱子
- `emerald` → 绿宝石箱子
- `redstone` → 红石箱子
- `lapis_lazuli` → 青金石箱子
- `quartz` → 石英箱子
- `gravel` → 沙砾箱子
- `oak_planks` → 橡木木板箱子
- `spruce_planks` → 云杉木板箱子
- ... 以此类推

## 工作原理

### 🔍 **分类决策流程**
```
物品 → 检查用户自定义分类 → 检查预配置分类组 → 检查颜色变种 → 使用物品名称作为分类
```

### 📋 **具体示例**

#### 火药的分类过程
1. 检查用户自定义分类 → 没有配置
2. 检查预配置分类组 → 不在任何组中
3. 检查颜色变种 → 不是颜色变种
4. **结果**：使用 `gunpowder` 作为分类名

#### 土豆的分类过程
1. 检查用户自定义分类 → 没有配置
2. 检查预配置分类组 → 在 `potato` 组中
3. **结果**：使用 `potato` 作为分类名

#### 白色羊毛的分类过程
1. 检查用户自定义分类 → 没有配置
2. 检查预配置分类组 → 不在任何组中
3. 检查颜色变种 → 是羊毛变种
4. **结果**：使用 `wool` 作为分类名

## 使用方法

### 基础设置
```bash
# 启用模块
itemsorter enable

# 添加源箱子
itemsorter addchest 100 64 200

# 系统会自动为每种物品分配箱子
# 无需额外配置！
```

### 自定义分类（可选）
```bash
# 如果你想要自定义某些物品的分类
itemsorter addcategory precious diamond emerald netherite_ingot

# 这样钻石、绿宝石、下界合金锭就会放在同一个"precious"箱子里
# 而不是各自单独的箱子
```

### 查看分类状态
```bash
# 查看所有分类配置
itemsorter listcategories

# 查看模块状态
itemsorter status

# 查看箱子缓存
itemsorter cachestats
```

## 仓库布局建议

### 🏗️ **区域化布局**

由于每种物品都有自己的箱子，建议按类型区域化布局：

```
农作物区域：
[火药箱] [甘蔗箱] [小麦箱] [胡萝卜箱] [甜菜根箱] [面包箱]

肉类区域：
[牛肉箱] [猪肉箱] [鸡肉箱] [羊肉箱] [兔肉箱] [鳕鱼箱] [鲑鱼箱]

矿物区域：
[钻石箱] [绿宝石箱] [红石箱] [青金石箱] [石英箱] [煤炭箱] [铁箱] [金箱]

建筑材料区域：
[石头箱] [泥土箱] [沙子箱] [沙砾箱] [橡木原木箱] [云杉原木箱] ...

木材区域：
[橡木木板箱] [云杉木板箱] [白桦木板箱] [丛林木板箱] ...

颜色物品区域：
[羊毛箱] [混凝土箱] [陶瓦箱] [玻璃箱] [地毯箱] [床箱]
```

### 📦 **箱子数量估算**

根据ItemRegistry.java中的物品数量，大约需要：
- **农作物/食物**：约50个箱子
- **建筑材料**：约100个箱子
- **工具/武器**：约30个箱子
- **装饰物品**：约80个箱子
- **其他物品**：约200个箱子

**总计**：约460个箱子（建议准备500个箱子）

## 优势特点

### ✅ **优点**
1. **极简配置** - 大部分物品无需配置，自动分类
2. **精确存储** - 每种物品都有专用箱子，绝不混合
3. **易于查找** - 物品名称就是箱子用途，直观明了
4. **灵活扩展** - 可以随时添加自定义分类覆盖默认行为
5. **智能合并** - 相关物品（如生熟肉）自动合并存储

### 🎯 **适用场景**
- **大型仓库** - 需要精确分类大量物品
- **自动化农场** - 每种产物都有专用存储
- **建筑项目** - 不同材料分开存储便于管理
- **收集爱好者** - 每种物品都有专门位置

### ⚠️ **注意事项**
1. **空间需求** - 需要大量箱子和存储空间
2. **初期布局** - 建议提前规划好区域布局
3. **箱子标记** - 可以用告示牌标记箱子用途

## 配置示例

### 完全自动配置
```bash
# 只需要这两个命令，其他全自动！
itemsorter enable
itemsorter addchest 100 64 200
```

### 部分自定义配置
```bash
# 基础配置
itemsorter enable
itemsorter addchest 100 64 200

# 自定义一些特殊分类
itemsorter addcategory building_tools scaffolding ladder torch lantern
itemsorter addcategory farming_supplies bone_meal wheat_seeds carrot potato
itemsorter addcategory pvp_gear diamond_sword diamond_helmet shield bow arrow

# 其他物品仍然使用缺省分类（物品名称作为分类名）
```

## 总结

这个缺省分类系统让物品管理变得极其简单：

1. **预配置的多物品组** - 相关物品自动合并（如生熟肉、矿物组合）
2. **缺省单物品分类** - 其他物品自动使用物品名称作为分类
3. **用户自定义覆盖** - 可以随时添加自定义分类覆盖默认行为

你只需要准备足够的空箱子，系统会自动为每种物品分配专用存储空间！
