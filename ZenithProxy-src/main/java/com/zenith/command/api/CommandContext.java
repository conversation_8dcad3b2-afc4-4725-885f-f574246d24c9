package com.zenith.command.api;

import com.zenith.discord.Embed;
import com.zenith.network.server.ServerSession;
import lombok.Data;
import org.jspecify.annotations.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class CommandContext {
    private final String input;
    private final CommandSource source;
    private final Embed embed;
    private final List<String> multiLineOutput;
    private @Nullable InGamePlayerInfo inGamePlayerInfo;
    // don't log sensitive input like passwords to discord
    private boolean sensitiveInput = false;
    private boolean noOutput = false;
    // can be used by plugins to pass additional arbitrary data to commands
    private final Map<String, Object> data = new HashMap<>(0);

    public CommandContext(String input, CommandSource source, Embed embed, List<String> multiLineOutput) {
        this.input = input;
        this.source = source;
        this.embed = embed;
        this.multiLineOutput = multiLineOutput;
    }

    public static CommandContext create(final String input, final CommandSource source) {
        return new CommandContext(input.trim(), source, new Embed(), new ArrayList<>(0));
    }

    public static CommandContext createInGamePlayerContext(String input, ServerSession session) {
        var context = create(input, CommandSources.PLAYER);
        context.setInGamePlayerInfo(new InGamePlayerInfo(session));
        return context;
    }

    public static CommandContext createSpectatorContext(String input, ServerSession session) {
        var context = create(input, CommandSources.SPECTATOR);
        context.setInGamePlayerInfo(new InGamePlayerInfo(session));
        return context;
    }

    public record InGamePlayerInfo(ServerSession session) {}
}
