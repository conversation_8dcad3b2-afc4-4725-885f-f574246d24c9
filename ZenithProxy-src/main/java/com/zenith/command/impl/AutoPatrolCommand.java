package com.zenith.command.impl;

import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.zenith.command.api.Command;
import com.zenith.command.api.CommandCategory;
import com.zenith.command.api.CommandContext;
import com.zenith.command.api.CommandUsage;
import com.zenith.discord.Embed;
import com.zenith.module.impl.AutoPatrol;
import com.zenith.util.config.Config;

import java.util.List;

import static com.mojang.brigadier.arguments.IntegerArgumentType.getInteger;
import static com.mojang.brigadier.arguments.IntegerArgumentType.integer;
import static com.mojang.brigadier.arguments.StringArgumentType.getString;
import static com.zenith.Globals.*;
import static com.zenith.command.brigadier.CustomStringArgumentType.wordWithChars;
import static com.zenith.command.brigadier.ToggleArgumentType.getToggle;
import static com.zenith.command.brigadier.ToggleArgumentType.toggle;

public class AutoPatrolCommand extends Command {
    @Override
    public CommandUsage commandUsage() {
        return CommandUsage.builder()
            .name("autoPatrol")
            .category(CommandCategory.MODULE)
            .description("""
            Automatically patrols between configured waypoints in sequence.
            Bot will move to each patrol point in order, then loop back to the first point.
            Includes stuck detection and unstuck actions.
            """)
            .usageLines(
                "on/off",
                "add <x> <y> <z> <name>",
                "remove <index>",
                "clear",
                "list",
                "goto <index>",
                "stuckDetection <seconds>",
                "arrivalThreshold <blocks>",
                "unstuckActions on/off",
                "jumpUnstuck on/off",
                "movementUnstuck on/off",
                "unstuckDuration <ticks>"
            )
            .build();
    }

    @Override
    public LiteralArgumentBuilder<CommandContext> register() {
        return command("autoPatrol")
            .then(argument("toggle", toggle()).executes(c -> {
                CONFIG.client.extra.autoPatrol.enabled = getToggle(c, "toggle");
                MODULE.get(AutoPatrol.class).syncEnabledFromConfig();
                c.getSource().getEmbed()
                    .title("AutoPatrol " + toggleStrCaps(CONFIG.client.extra.autoPatrol.enabled));
                return OK;
            }))
            .then(literal("add")
                .then(argument("x", integer()).then(argument("y", integer()).then(argument("z", integer())                .then(argument("name", wordWithChars()).executes(c -> {
                    int x = getInteger(c, "x");
                    int y = getInteger(c, "y");
                    int z = getInteger(c, "z");
                    String name = getString(c, "name");
                    
                    MODULE.get(AutoPatrol.class).addPatrolPoint(x, y, z, name);
                    c.getSource().getEmbed()
                        .title("Patrol Point Added")
                        .description("Added patrol point: " + name + " (" + x + ", " + y + ", " + z + ")");
                    return OK;
                }))))))
            .then(literal("remove").then(argument("index", integer()).executes(c -> {
                int index = getInteger(c, "index") - 1; // Convert to 0-based index
                List<Config.Client.Extra.AutoPatrol.PatrolPoint> points = CONFIG.client.extra.autoPatrol.patrolPoints;
                
                if (index >= 0 && index < points.size()) {
                    Config.Client.Extra.AutoPatrol.PatrolPoint removed = points.remove(index);
                    c.getSource().getEmbed()
                        .title("Patrol Point Removed")
                        .description("Removed patrol point: " + removed.name + " (" + removed.x + ", " + removed.y + ", " + removed.z + ")");
                    return OK;
                } else {
                    c.getSource().getEmbed()
                        .title("Invalid Index")
                        .description("Patrol point index " + (index + 1) + " does not exist. Use 'list' to see available points.");
                    return ERROR;
                }
            })))
            .then(literal("clear").executes(c -> {
                MODULE.get(AutoPatrol.class).clearPatrolPoints();
                c.getSource().getEmbed()
                    .title("Patrol Points Cleared")
                    .description("All patrol points have been removed.");
                return OK;
            }))
            .then(literal("list").executes(c -> {
                List<Config.Client.Extra.AutoPatrol.PatrolPoint> points = CONFIG.client.extra.autoPatrol.patrolPoints;
                AutoPatrol module = MODULE.get(AutoPatrol.class);
                int currentIndex = module.getCurrentPatrolIndex();
                
                if (points.isEmpty()) {
                    c.getSource().getEmbed()
                        .title("No Patrol Points")
                        .description("No patrol points configured. Use 'add' to add points.");
                } else {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < points.size(); i++) {
                        Config.Client.Extra.AutoPatrol.PatrolPoint point = points.get(i);
                        String marker = (i == currentIndex) ? "➤ " : "  ";
                        sb.append(marker).append(i + 1).append(". ").append(point.name)
                          .append(" (").append(point.x).append(", ").append(point.y).append(", ").append(point.z).append(")\n");
                    }
                    
                    c.getSource().getEmbed()
                        .title("Patrol Points")
                        .description(sb.toString());
                }
                return OK;
            }))
            .then(literal("goto").then(argument("index", integer()).executes(c -> {
                int index = getInteger(c, "index") - 1; // Convert to 0-based index
                MODULE.get(AutoPatrol.class).setCurrentPatrolIndex(index);
                c.getSource().getEmbed()
                    .title("Patrol Index Set")
                    .description("Current patrol index set to " + (index + 1));
                return OK;
            })))
            .then(literal("stuckDetection").then(argument("seconds", integer(1, 60)).executes(c -> {
                int seconds = getInteger(c, "seconds");
                CONFIG.client.extra.autoPatrol.stuckDetectionSeconds = seconds;
                c.getSource().getEmbed()
                    .title("Stuck Detection Updated")
                    .description("Stuck detection set to " + seconds + " seconds");
                return OK;
            })))
            .then(literal("arrivalThreshold").then(argument("blocks", integer(1, 10)).executes(c -> {
                int blocks = getInteger(c, "blocks");
                CONFIG.client.extra.autoPatrol.arrivalThreshold = blocks;
                c.getSource().getEmbed()
                    .title("Arrival Threshold Updated")
                    .description("Arrival threshold set to " + blocks + " blocks");
                return OK;
            })))
            .then(literal("unstuckActions").then(argument("toggle", toggle()).executes(c -> {
                CONFIG.client.extra.autoPatrol.enableUnstuckActions = getToggle(c, "toggle");
                c.getSource().getEmbed()
                    .title("Unstuck Actions " + toggleStrCaps(CONFIG.client.extra.autoPatrol.enableUnstuckActions));
                return OK;
            })))
            .then(literal("jumpUnstuck").then(argument("toggle", toggle()).executes(c -> {
                CONFIG.client.extra.autoPatrol.enableJumpUnstuck = getToggle(c, "toggle");
                c.getSource().getEmbed()
                    .title("Jump Unstuck " + toggleStrCaps(CONFIG.client.extra.autoPatrol.enableJumpUnstuck));
                return OK;
            })))
            .then(literal("movementUnstuck").then(argument("toggle", toggle()).executes(c -> {
                CONFIG.client.extra.autoPatrol.enableMovementUnstuck = getToggle(c, "toggle");
                c.getSource().getEmbed()
                    .title("Movement Unstuck " + toggleStrCaps(CONFIG.client.extra.autoPatrol.enableMovementUnstuck));
                return OK;
            })))
            .then(literal("unstuckDuration").then(argument("ticks", integer(10, 200)).executes(c -> {
                int ticks = getInteger(c, "ticks");
                CONFIG.client.extra.autoPatrol.unstuckActionDurationTicks = ticks;
                c.getSource().getEmbed()
                    .title("Unstuck Duration Updated")
                    .description("Unstuck action duration set to " + ticks + " ticks");
                return OK;
            })));
    }

    @Override
    public void defaultEmbed(Embed embed) {
        embed.primaryColor();
        if (!embed.isDescriptionPresent()) {
            AutoPatrol module = MODULE.get(AutoPatrol.class);
            List<Config.Client.Extra.AutoPatrol.PatrolPoint> points = CONFIG.client.extra.autoPatrol.patrolPoints;
            
            embed
                .addField("AutoPatrol", toggleStr(CONFIG.client.extra.autoPatrol.enabled), false)
                .addField("Patrol Points", String.valueOf(points.size()), false)
                .addField("Current Index", module.getCurrentPatrolIndex() + 1 + "/" + points.size(), false)
                .addField("Stuck Detection", CONFIG.client.extra.autoPatrol.stuckDetectionSeconds + "s", false)
                .addField("Arrival Threshold", CONFIG.client.extra.autoPatrol.arrivalThreshold + " blocks", false)
                .addField("Unstuck Actions", toggleStr(CONFIG.client.extra.autoPatrol.enableUnstuckActions), false)
                .addField("Jump Unstuck", toggleStr(CONFIG.client.extra.autoPatrol.enableJumpUnstuck), false)
                .addField("Movement Unstuck", toggleStr(CONFIG.client.extra.autoPatrol.enableMovementUnstuck), false)
                .addField("Unstuck Duration", CONFIG.client.extra.autoPatrol.unstuckActionDurationTicks + " ticks", false);
        }
    }
} 