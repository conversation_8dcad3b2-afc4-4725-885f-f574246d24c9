package com.zenith.module.impl;

import com.zenith.event.client.ClientBotTick;
import com.zenith.feature.pathfinder.goals.Goal;
import com.zenith.feature.pathfinder.goals.GoalBlock;
import com.zenith.feature.player.Input;
import com.zenith.feature.player.InputRequest;
import com.zenith.mc.block.BlockPos;
import com.zenith.module.api.Module;
import com.zenith.util.config.Config;
import com.zenith.util.math.MathHelper;
import com.zenith.util.timer.Timer;
import com.zenith.util.timer.Timers;
import com.zenith.Proxy;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.ServerboundChatPacket;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static com.github.rfresh2.EventConsumer.of;
import static com.zenith.Globals.*;

public class AutoPatrol extends Module {
    private final Timer stuckTimer = Timers.tickTimer();
    private final Timer unstuckActionTimer = Timers.tickTimer();
    public static final int MOVEMENT_PRIORITY = 160;
    
    // 巡逻状态
    private int currentPatrolIndex = 0;
    private double lastX = Double.MIN_VALUE;
    private double lastY = Double.MIN_VALUE;
    private double lastZ = Double.MIN_VALUE;
    private long lastPositionChangeTime = 0;
    private boolean isUnstucking = false;
    private ScheduledFuture<?> unstuckActionFuture = null;
    
    @Override
    public void subscribeEvents() {
        EVENT_BUS.subscribe(
            this,
            of(ClientBotTick.class, this::handleBotTick),
            of(ClientBotTick.Starting.class, this::handleBotTickStarting)
        );
    }

    @Override
    public boolean enabledSetting() {
        return CONFIG.client.extra.autoPatrol.enabled;
    }

    @Override
    public void onDisable() {
        BARITONE.stop();
        currentPatrolIndex = 0;
        isUnstucking = false;
        if (unstuckActionFuture != null) {
            unstuckActionFuture.cancel(true);
            unstuckActionFuture = null;
        }
    }

    private void handleBotTickStarting(ClientBotTick.Starting event) {
        stuckTimer.reset();
        unstuckActionTimer.reset();
        lastPositionChangeTime = System.currentTimeMillis();
    }

    private void handleBotTick(ClientBotTick event) {
        if (!CONFIG.client.extra.autoPatrol.enabled) return;
        
        List<Config.Client.Extra.AutoPatrol.PatrolPoint> patrolPoints = CONFIG.client.extra.autoPatrol.patrolPoints;
        if (patrolPoints.isEmpty()) {
            debug("No patrol points configured");
            return;
        }

        // 检查位置变化
        checkPositionChange();
        
        // 检查是否卡住
        if (checkIfStuck()) {
            handleStuck();
            return;
        }

        // 如果正在解锁操作，不进行巡逻
        if (isUnstucking) {
            return;
        }

        // 执行巡逻逻辑
        performPatrol(patrolPoints);
    }

    private void checkPositionChange() {
        double currentX = CACHE.getPlayerCache().getX();
        double currentY = CACHE.getPlayerCache().getY();
        double currentZ = CACHE.getPlayerCache().getZ();
        
        double distance = MathHelper.distance3d(lastX, lastY, lastZ, currentX, currentY, currentZ);
        
        if (distance > 0.1) { // 有显著位移
            lastPositionChangeTime = System.currentTimeMillis();
            lastX = currentX;
            lastY = currentY;
            lastZ = currentZ;
        }
    }

    private boolean checkIfStuck() {
        long stuckTime = System.currentTimeMillis() - lastPositionChangeTime;
        int stuckDetectionMs = CONFIG.client.extra.autoPatrol.stuckDetectionSeconds * 1000;
        
        return stuckTime > stuckDetectionMs && !isUnstucking;
    }

    private void handleStuck() {
        if (!CONFIG.client.extra.autoPatrol.enableUnstuckActions) {
            warn("Bot appears to be stuck for {} seconds", CONFIG.client.extra.autoPatrol.stuckDetectionSeconds);
            return;
        }

        info("Bot appears to be stuck, attempting unstuck actions");
        isUnstucking = true;
        
        // 停止当前pathfinder
        BARITONE.stop();
        
        // 执行解锁操作
        performUnstuckActions();
    }

    private void performUnstuckActions() {
        if (unstuckActionFuture != null && !unstuckActionFuture.isDone()) {
            unstuckActionFuture.cancel(true);
        }

        unstuckActionFuture = Proxy.getInstance().getClient().getClientEventLoop().scheduleAtFixedRate(() -> {
            if (!isUnstucking) {
                unstuckActionFuture.cancel(true);
                return;
            }

            // 跳跃解锁
            if (CONFIG.client.extra.autoPatrol.enableJumpUnstuck) {
                var jumpInput = Input.builder()
                    .jumping(true)
                    .build();
                var jumpRequest = InputRequest.builder()
                    .owner(this)
                    .input(jumpInput)
                    .priority(MOVEMENT_PRIORITY)
                    .build();
                INPUTS.submit(jumpRequest);
            }

            // 轻微移动解锁
            if (CONFIG.client.extra.autoPatrol.enableMovementUnstuck) {
                // 随机选择前/后/左/右方向
                int dir = ThreadLocalRandom.current().nextInt(4);
                double dx = 0, dz = 0;
                switch (dir) {
                    case 0 -> dx = 0.5;   // forward (positive X)
                    case 1 -> dx = -0.5;  // backward (negative X)
                    case 2 -> dz = 0.5;   // right (positive Z)
                    case 3 -> dz = -0.5;  // left (negative Z)
                }
                double px = CACHE.getPlayerCache().getX() + dx;
                double py = CACHE.getPlayerCache().getY();
                double pz = CACHE.getPlayerCache().getZ() + dz;
                Goal moveGoal = new GoalBlock((int)Math.round(px), (int)Math.round(py), (int)Math.round(pz));
                BARITONE.pathTo(moveGoal);
            }

        }, 0, 50, TimeUnit.MILLISECONDS);

        // 设置解锁操作持续时间
        Proxy.getInstance().getClient().getClientEventLoop().schedule(() -> {
            if (unstuckActionFuture != null) {
                unstuckActionFuture.cancel(true);
                unstuckActionFuture = null;
            }
            isUnstucking = false;
            lastPositionChangeTime = System.currentTimeMillis(); // 重置卡住检测
            info("Unstuck actions completed");
        }, CONFIG.client.extra.autoPatrol.unstuckActionDurationTicks * 50L, TimeUnit.MILLISECONDS);
    }

    private void performPatrol(List<Config.Client.Extra.AutoPatrol.PatrolPoint> patrolPoints) {
        if (patrolPoints.isEmpty()) return;

        // 获取当前目标点
        Config.Client.Extra.AutoPatrol.PatrolPoint currentPoint = patrolPoints.get(currentPatrolIndex);
        
        // 检查是否到达当前目标点
        double distanceToTarget = MathHelper.distance3d(
            CACHE.getPlayerCache().getX(),
            CACHE.getPlayerCache().getY(),
            CACHE.getPlayerCache().getZ(),
            currentPoint.x,
            currentPoint.y,
            currentPoint.z
        );

        if (distanceToTarget <= CONFIG.client.extra.autoPatrol.arrivalThreshold) {
            // 到达目标点，切换到下一个
            info("Reached patrol point {}: {} ({}, {}, {})", 
                currentPatrolIndex + 1, currentPoint.name, currentPoint.x, currentPoint.y, currentPoint.z);
            
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.size();
            
            // 获取下一个目标点
            Config.Client.Extra.AutoPatrol.PatrolPoint nextPoint = patrolPoints.get(currentPatrolIndex);
            info("Moving to next patrol point {}: {} ({}, {}, {})", 
                currentPatrolIndex + 1, nextPoint.name, nextPoint.x, nextPoint.y, nextPoint.z);
        }

        // 如果pathfinder不活跃，开始移动到当前目标点
        if (!BARITONE.isActive()) {
            Config.Client.Extra.AutoPatrol.PatrolPoint targetPoint = patrolPoints.get(currentPatrolIndex);
            Goal goal = new GoalBlock(targetPoint.x, targetPoint.y, targetPoint.z);
            
            debug("Pathing to patrol point {}: {} ({}, {}, {})", 
                currentPatrolIndex + 1, targetPoint.name, targetPoint.x, targetPoint.y, targetPoint.z);
            
            BARITONE.pathTo(goal);
        }
    }

    public void addPatrolPoint(int x, int y, int z, String name) {
        CONFIG.client.extra.autoPatrol.patrolPoints.add(
            new Config.Client.Extra.AutoPatrol.PatrolPoint(x, y, z, name)
        );
        info("Added patrol point: {} ({}, {}, {})", name, x, y, z);
    }

    public void clearPatrolPoints() {
        CONFIG.client.extra.autoPatrol.patrolPoints.clear();
        currentPatrolIndex = 0;
        info("Cleared all patrol points");
    }

    public void setCurrentPatrolIndex(int index) {
        if (index >= 0 && index < CONFIG.client.extra.autoPatrol.patrolPoints.size()) {
            currentPatrolIndex = index;
            info("Set current patrol index to {}", index + 1);
        } else {
            warn("Invalid patrol index: {}", index + 1);
        }
    }

    public List<Config.Client.Extra.AutoPatrol.PatrolPoint> getPatrolPoints() {
        return new ArrayList<>(CONFIG.client.extra.autoPatrol.patrolPoints);
    }

    public int getCurrentPatrolIndex() {
        return currentPatrolIndex;
    }

    public boolean isPatrolling() {
        return CONFIG.client.extra.autoPatrol.enabled && !CONFIG.client.extra.autoPatrol.patrolPoints.isEmpty();
    }
} 