package com.zenith.plugin.impl;

import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.zenith.command.api.Command;
import com.zenith.command.api.CommandCategory;
import com.zenith.command.api.CommandContext;
import com.zenith.command.api.CommandUsage;
import com.zenith.discord.Embed;
import net.kyori.adventure.text.Component;

import java.util.List;

import static com.zenith.command.brigadier.CustomStringArgumentType.getString;
import static com.zenith.command.brigadier.CustomStringArgumentType.wordWithChars;
import static com.mojang.brigadier.arguments.IntegerArgumentType.getInteger;

import static com.zenith.Globals.MODULE;

public class AutoVaultOpenerCommand extends Command {
    private final AutoVaultOpenerConfig config;

    public AutoVaultOpenerCommand(AutoVaultOpenerConfig config) {
        this.config = config;
    }

    @Override
    public CommandUsage commandUsage() {
        return CommandUsage.builder()
                .name("autovault")
                .category(CommandCategory.MODULE)
                .description("""
                        自动开宝库插件控制命令
                        """)
                .usageLines(
                        "start - 开始自动开宝库",
                        "stop - 停止自动开宝库",
                        "status - 查看当前状态",
                        "progress - 查看当前进度",
                        "reset - 重置进度到第一个账号",
                        "reload - 重新加载配置",
                        "add <username> <password> [email] - 添加账号",
                        "list - 列出所有账号",
                        "remove <index> - 移除指定账号",
                        "help - 显示帮助"
                )
                .build();
    }

    @Override
    public LiteralArgumentBuilder<CommandContext> register() {
        return command("autovault")
                .then(literal("start").executes(c -> {
                    if (config.accounts.isEmpty()) {
                        c.getSource().getEmbed()
                                .title("错误")
                                .description("没有配置账号，请先添加账号");
                        return ERROR;
                    }
                    if (!config.enabled) {
                        config.enabled = true;
                        MODULE.get(AutoVaultOpenerModule.class).syncEnabledFromConfig();
                        c.getSource().getEmbed()
                                .title("已启用")
                                .description("自动开宝库插件已启用");
                    } else {
                        c.getSource().getEmbed()
                                .title("已运行")
                                .description("自动开宝库插件已在运行中");
                    }
                    return OK;
                }))
                .then(literal("stop").executes(c -> {
                    config.enabled = false;
                    MODULE.get(AutoVaultOpenerModule.class).syncEnabledFromConfig();
                    c.getSource().getEmbed()
                            .title("已停止")
                            .description("自动开宝库插件已停止");
                    return OK;
                }))
                .then(literal("status").executes(c -> {
                    var embed = c.getSource().getEmbed()
                            .title("自动开宝库状态")
                            .description("插件状态: " + (config.enabled ? "启用" : "禁用"));
                    embed.addField("账号数量", String.valueOf(config.accounts.size()), true);
                    embed.addField("当前进度", String.valueOf(config.currentAccountIndex + 1) + "/" + config.accounts.size(), true);
                    embed.addField("循环模式", config.loopAccounts ? "是" : "否", true);
                    embed.addField("登录命令", config.loginCommand, true);
                    embed.addField("搜索范围", String.valueOf(config.searchRadius), true);
                    return OK;
                }))
                .then(literal("progress").executes(c -> {
                    var embed = c.getSource().getEmbed().title("当前进度");
                    if (config.accounts.isEmpty()) {
                        embed.description("没有配置账号");
                        return OK;
                    }
                    
                    if (config.currentAccountIndex >= config.accounts.size()) {
                        embed.description("所有账号已完成，下次启动将重新开始");
                    } else {
                        AutoVaultOpenerConfig.AccountInfo currentAccount = config.accounts.get(config.currentAccountIndex);
                        embed.description("当前处理第 " + (config.currentAccountIndex + 1) + "/" + config.accounts.size() + " 个账号")
                                .addField("当前账号", currentAccount.username, true);
                    }
                    return OK;
                }))
                .then(literal("reset").executes(c -> {
                    config.currentAccountIndex = 0;
                    c.getSource().getEmbed()
                            .title("进度已重置")
                            .description("进度已重置到第一个账号");
                    return OK;
                }))
                .then(literal("reload").executes(c -> {
                    // 这里可以重新加载配置文件
                    c.getSource().getEmbed()
                            .title("配置重载")
                            .description("配置已重新加载");
                    return OK;
                }))
                .then(literal("add")
                        .then(argument("username", wordWithChars())
                                .then(argument("password", wordWithChars())
                                        .then(argument("email", wordWithChars()).executes(c -> {
                                            String username = getString(c, "username");
                                            String password = getString(c, "password");
                                            String email = getString(c, "email");
                                            AutoVaultOpenerConfig.AccountInfo account = new AutoVaultOpenerConfig.AccountInfo(username, password, email);
                                            config.accounts.add(account);
                                            c.getSource().getEmbed()
                                                    .title("账号已添加")
                                                    .description("用户名: " + username);
                                            return OK;
                                        }))
                                        .executes(c -> {
                                            String username = getString(c, "username");
                                            String password = getString(c, "password");
                                            AutoVaultOpenerConfig.AccountInfo account = new AutoVaultOpenerConfig.AccountInfo(username, password);
                                            config.accounts.add(account);
                                            c.getSource().getEmbed()
                                                    .title("账号已添加")
                                                    .description("用户名: " + username);
                                            return OK;
                                        })
                                )
                        )
                )
                .then(literal("list").executes(c -> {
                    var embed = c.getSource().getEmbed().title("账号列表");
                    if (config.accounts.isEmpty()) {
                        embed.description("没有配置账号");
                        return OK;
                    }
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < config.accounts.size(); i++) {
                        AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(i);
                        sb.append(i + 1).append(". ").append(account.username);
                        if (account.email != null && !account.email.isEmpty()) {
                            sb.append(" (").append(account.email).append(")");
                        }
                        sb.append("\n");
                    }
                    embed.description(sb.toString());
                    return OK;
                }))
                .then(literal("remove")
                        .then(argument("index", IntegerArgumentType.integer(1)).executes(c -> {
                            int index = getInteger(c, "index") - 1;
                            if (index >= 0 && index < config.accounts.size()) {
                                AutoVaultOpenerConfig.AccountInfo removed = config.accounts.remove(index);
                                c.getSource().getEmbed()
                                        .title("账号已移除")
                                        .description("已移除账号: " + removed.username);
                            } else {
                                c.getSource().getEmbed()
                                        .title("索引错误")
                                        .description("无效的账号索引");
                            }
                            return OK;
                        })
                ))
                .then(literal("help").executes(c -> {
                    var embed = c.getSource().getEmbed().title("自动开宝库帮助");
                    StringBuilder sb = new StringBuilder();
                    sb.append("**start** - 开始自动开宝库\n");
                    sb.append("**stop** - 停止自动开宝库\n");
                    sb.append("**status** - 查看当前状态\n");
                    sb.append("**progress** - 查看当前进度\n");
                    sb.append("**reset** - 重置进度到第一个账号\n");
                    sb.append("**reload** - 重新加载配置\n");
                    sb.append("**add** - 添加账号\n");
                    sb.append("**list** - 列出所有账号\n");
                    sb.append("**remove** - 移除指定账号\n");
                    sb.append("**help** - 显示此帮助\n");
                    embed.description(sb.toString());
                    return OK;
                }));
    }

    @Override
    public void defaultEmbed(Embed embed) {
        embed.primaryColor()
                .addField("Enabled", toggleStr(config.enabled))
                .addField("Accounts", String.valueOf(config.accounts.size()))
                .addField("Loop Mode", config.loopAccounts ? "Yes" : "No")
                .addField("LoginCmd", config.loginCommand)
                .addField("SearchRadius", String.valueOf(config.searchRadius));
    }
}
