package com.zenith.plugin.impl;

import com.zenith.plugin.api.Plugin;
import com.zenith.plugin.api.PluginAPI;
import com.zenith.plugin.api.ZenithProxyPlugin;

@Plugin(
    id = "auto-vault-opener",
    version = "1.0.0",
    description = "自动开宝库插件 - 轮流登录账号自动开宝库",
    authors = {"AI Assistant"}
)
public class AutoVaultOpener implements ZenithProxyPlugin {
    
    @Override
    public void onLoad(PluginAPI pluginAPI) {
        // 注册配置
        AutoVaultOpenerConfig config = pluginAPI.registerConfig("auto-vault-opener", AutoVaultOpenerConfig.class);
        
        // 注册模块
        AutoVaultOpenerModule module = new AutoVaultOpenerModule(config);
        pluginAPI.registerModule(module);
        
        // 注册命令
        AutoVaultOpenerCommand command = new AutoVaultOpenerCommand(config);
        pluginAPI.registerCommand(command);
        
        pluginAPI.getLogger().info("自动开宝库插件已加载");
    }
} 