package com.zenith.plugin.impl;

import java.util.ArrayList;
import java.util.List;

public class AutoVaultOpenerConfig {
    // 是否启用插件
    public boolean enabled = true;

    public boolean esp = false;

    public final AutoOpenModuleConfig exampleModule = new AutoOpenModuleConfig();

    public static class AutoOpenModuleConfig {
        public boolean enabled = true;
        public int delayTicks = 250;
    }

    public final ExampleWanderConfig wander = new ExampleWanderConfig();

    public static final class ExampleWanderConfig {
        public boolean enabled = false;
        public int radius = 2000;
        public int minRadius = 100;
    }
    
    // 宝库目标信息
    public static class VaultInfo {
        public int vaultX, vaultY, vaultZ;
        public int buttonX, buttonY, buttonZ;
    }
    // 移除全局 vaults/target/button 坐标相关字段
    // public List<VaultInfo> vaults; 


    // 账号列表
    public List<AccountInfo> accounts = new ArrayList<>();

    // 登录命令
    public String loginCommand = "/login";

    // 当前处理到的账号索引（用于记录进度）
    public int currentAccountIndex = 0;

    // 是否循环执行所有账号（true=循环，false=结束）
    public boolean loopAccounts = true;

    // 等待时间设置（秒）
    public int waitAfterShulkerOpen = 1;
    public int waitAfterVaultOpen = 1;
    public int waitBetweenAccounts = 1;

    // 搜索范围
    public int searchRadius = 5;

    // 重试设置
    public int maxRetries = 3;
    public int retryDelaySeconds = 3;

    // 账号信息类
    public static class AccountInfo {
        public String username;
        public String password;
        public String email; // 用于MSA登录
        public List<VaultInfo> vaults = new ArrayList<>(); // 每个账号的宝库目标

        public AccountInfo() {
        }

        public AccountInfo(String username, String password) {
            this.username = username;
            this.password = password;
        }

        public AccountInfo(String username, String password, String email) {
            this.username = username;
            this.password = password;
            this.email = email;
        }
    }
}
