package com.zenith.plugin.impl;

 
import com.github.rfresh2.EventConsumer;
import com.zenith.Globals;
import com.zenith.Proxy;
import com.zenith.cache.data.chunk.ChunkCache;
import com.zenith.cache.data.inventory.Container;
import com.zenith.event.chat.SystemChatEvent;
import com.zenith.event.client.ClientBotTick;
import com.zenith.event.client.ClientConnectEvent;
import com.zenith.event.client.ClientDisconnectEvent;
import com.zenith.event.client.ClientOnlineEvent;
import com.zenith.feature.inventory.InventoryActionRequest;
import com.zenith.feature.inventory.actions.DropItem;
import com.zenith.feature.pathfinder.BlockStateInterface;
import com.zenith.feature.pathfinder.goals.GoalBlock;
import com.zenith.feature.player.InputRequest;
import com.zenith.feature.player.RotationHelper;
import com.zenith.feature.player.World;
import com.zenith.feature.player.raycast.RaycastHelper;
import com.zenith.mc.block.BlockRegistry;
import com.zenith.mc.block.properties.VaultState;
import com.zenith.mc.block.properties.api.BlockStateProperties;
import com.zenith.mc.item.ItemData;
import com.zenith.mc.item.ItemRegistry;
import com.zenith.module.impl.AbstractInventoryModule;
import com.zenith.network.client.Authenticator;
import org.geysermc.mcprotocollib.protocol.data.game.entity.object.Direction;
import org.geysermc.mcprotocollib.protocol.data.game.entity.player.Hand;
import org.geysermc.mcprotocollib.protocol.data.game.inventory.DropItemAction;
import org.geysermc.mcprotocollib.protocol.data.game.item.ItemStack;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.ServerboundChatPacket;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.inventory.ServerboundContainerClosePacket;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.player.ServerboundUseItemOnPacket;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.github.rfresh2.EventConsumer.of;
import static com.zenith.Globals.*;
import static com.zenith.util.config.Config.Authentication.AccountType.OFFLINE;

public class AutoVaultOpenerModule extends AbstractInventoryModule {

    private final AutoVaultOpenerConfig config;

    // 状态管理
    private volatile boolean isRunning = false;
    private volatile int currentAccountIndex = 0;
    private volatile int retryCount = 0;
    private volatile Instant lastActionTime = Instant.now();
    private volatile ScheduledFuture<?> nextActionFuture;
    private boolean reachedTargetPosition = false;
    private int delay = 0;
    private boolean isUsingKey = false;

    private Instant lastShulkerSearchTime = Instant.EPOCH;

    private volatile int currentVaultIndex = 0;

    // 当前状态
    public enum State {
        IDLE,
        CONNECTING,
        WAITING_FOR_LOGIN,
        WAITING_FOR_INGAME,
        GOTO_LOCATION,
        GETTING_KEY,
        TAKING_KEY,
        SEARCHING_VAULT,
        OPENING_VAULT,
        WAITING_VAULT,
        DISCONNECTING,
        WAITING_BETWEEN_ACCOUNTS
    }

    private volatile State currentState = State.IDLE;


    public AutoVaultOpenerModule(AutoVaultOpenerConfig config) {
        super(HandRestriction.MAIN_HAND, 0, 600);
        this.config = config;
        this.currentAccountIndex = config.currentAccountIndex;
    }

    @Override
    public boolean itemPredicate(ItemStack itemStack) {
        ItemData itemData = ItemRegistry.REGISTRY.get(itemStack.getId());
        return itemData != null && itemData == ItemRegistry.OMINOUS_TRIAL_KEY;
    }

    @Override
    public List<EventConsumer<?>> registerEvents() {
        return List.of(
                of(ClientConnectEvent.class, this::onClientConnect),
                of(ClientOnlineEvent.class, this::onClientOnline),
                of(ClientDisconnectEvent.class, this::onClientDisconnect),
                of(ClientBotTick.class, this::onClientTick),
                of(SystemChatEvent.class, this::onSystemChat)
        );
    }

    @Override
    public boolean enabledSetting() {
        return config.enabled;
    }

    @Override
    public void onEnable() {
        info("自动开宝库模块已启用");
        if (config.accounts.isEmpty()) {
            warn("没有配置账号，请先在配置文件中添加账号信息");
            return;
        }

        // 显示当前进度信息
        if (config.currentAccountIndex >= config.accounts.size()) {
            info("所有账号已完成");
            if (config.loopAccounts) {
                info("配置为循环，将重新开始");
                config.currentAccountIndex = 0;
            } else {
                info("配置为结束，将停止自动开宝库流程");
                stopVaultOpening();
                return;
            }
        } else {
            info("当前进度：第 {}/{} 个账号", config.currentAccountIndex + 1, config.accounts.size());
        }
        currentVaultIndex = 0;
        reachedTargetPosition = false;
        startVaultOpening();
    }

    @Override
    public void onDisable() {
        info("自动开宝库模块已禁用");
        stopVaultOpening();
    }

    private void startVaultOpening() {
        if (isRunning) return;
        isRunning = true;
        retryCount = 0;
        info("开始自动开宝库流程，从第 {} 个账号开始", config.currentAccountIndex + 1);
        processNextAccount();
    }

    private void stopVaultOpening() {
        isRunning = false;
        if (nextActionFuture != null) {
            nextActionFuture.cancel(false);
            nextActionFuture = null;
        }
        currentState = State.IDLE;
    }

    private void processNextAccount() {
        if (!isRunning) return;

        if (config.currentAccountIndex >= config.accounts.size()) {
            info("所有账号已完成");
            if (config.loopAccounts) {
                info("配置为循环，将重新开始");
                config.currentAccountIndex = 0;
            } else {
                info("配置为结束，将停止自动开宝库流程");
                stopVaultOpening();
                return;
            }
        }

        currentVaultIndex = 0;
        AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
        info("处理账号: {} (第 {}/{} 个)", account.username, config.currentAccountIndex + 1, config.accounts.size());

        // 设置认证信息
        CONFIG.authentication.username = account.username;
        CONFIG.authentication.password = account.password;
        if (account.email != null && !account.email.isEmpty()) {
            CONFIG.authentication.email = account.email;
        }
        Globals.CONFIG.server.verifyUsers = false;
        CONFIG.authentication.accountType = OFFLINE;

        Proxy.getInstance().cancelLogin();
        Authenticator.INSTANCE.clearAuthCache();

        currentState = State.CONNECTING;
        Proxy.getInstance().connect();
    }

    private void onClientConnect(ClientConnectEvent event) {
        if (!isRunning) return;
        info("客户端连接中...");
        currentState = State.WAITING_FOR_LOGIN;
    }

    private void onClientOnline(ClientOnlineEvent event) {
        if (!isRunning) return;
        info("客户端已上线，准备发送登录命令");
        currentState = State.WAITING_FOR_LOGIN;

    }

    private void onClientDisconnect(ClientDisconnectEvent event) {
        if (!isRunning) return;
        info("客户端断开连接");

        if (currentState == State.DISCONNECTING) {
            // 正常断开，处理下一个账号
            config.currentAccountIndex++;
            saveConfig(); // 保存进度
            scheduleAction(this::processNextAccount, config.waitBetweenAccounts);
        } else {
            // 意外断开，重试
            handleError("意外断开连接");
        }
    }

    private void onClientTick(ClientBotTick event) {
        if (!isRunning) return;

        // 检查是否卡住
        if (Duration.between(lastActionTime, Instant.now()).getSeconds() > 60) {
            warn("操作超时");
//            handleError("操作超时");

            AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
            if (currentVaultIndex + 1 < account.vaults.size()) {
                info("宝库1操作超时，开下一个");
                currentVaultIndex++;
                reachedTargetPosition = false;
                currentState = State.WAITING_FOR_INGAME;
                gotoLocation();
            } else {
                info("宝库2操作超时，断开连接");
                currentState = State.DISCONNECTING;
                reachedTargetPosition = false;
                currentVaultIndex = 0;
                Proxy.getInstance().disconnect();
            }

        }

        if (currentState == State.WAITING_FOR_INGAME) {
            gotoLocation();
        }
    }

    private void onSystemChat(SystemChatEvent event) {
        if (!isRunning) return;

        String message = event.message();

        // 检查登录是否成功 Logged-in due to Session Reconnection.
        if (message.contains("/login")) {
            info("bot登录中");

            AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
            sendChatMessage(config.loginCommand + " " + account.password);
        }

        if (message.contains("/email add")) {
            info("邮箱绑定");
            AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);

            String email = account.username + "@bbbbbbb.top";
            sendChatMessage("/email add " + email + " " + email);
        }

        // 检查登录是否成功 Logged-in due to Session Reconnection.
        if (message.contains("Logged-in") || message.contains("成功登录")) {
            info("bot登录成功");
//            currentState = State.WAITING_FOR_INGAME;
        }

        // 检查登录是否成功 Logged-in due to Session Reconnection.
        if (message.contains("Connecting to the server")) {
            info("bot 进入游戏服中");
            currentState = State.WAITING_FOR_INGAME;
        }


        // 检查是否被踢出
        if (message.contains("kicked") || message.contains("disconnected")) {
            warn("bot被服务器踢出");
            handleError("被服务器踢出");
        }
    }


    private void gotoLocation() {
        if (!isRunning) return;

        AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
        AutoVaultOpenerConfig.VaultInfo vault = account.vaults.get(currentVaultIndex);

        // 限流：确保每次执行间隔不低于1秒
        if (Duration.between(lastShulkerSearchTime, Instant.now()).toMillis() < 1000) {
            if (!reachedTargetPosition) {
                return;
            }
        }
        lastShulkerSearchTime = Instant.now();

//        CACHE.getPlayerCache().distanceSqToSelf()
        // 判断当前位置与目标点XZ距离
        int playerX = (int) CACHE.getPlayerCache().getX();
        int playerY = (int) CACHE.getPlayerCache().getY();
        int playerZ = (int) CACHE.getPlayerCache().getZ();
        double distance = Math.sqrt(
                Math.pow(playerX - vault.vaultX, 2) +
                        Math.pow(playerY - vault.vaultY, 2) +
                        Math.pow(playerZ - vault.vaultZ, 2)
        );

        if (distance >= 200) {
            info("距离目标坐标过远（{}，玩家：{}，{}，{}，目标：{}，{}，{}），不执行寻路和后续操作", distance,
                    playerX, playerY, playerZ,
                    vault.vaultX, vault.vaultY, vault.vaultZ);

            return;
        }

        currentState = State.GOTO_LOCATION;


        if (!reachedTargetPosition) {
            GoalBlock targetGoal = new GoalBlock(vault.vaultX, vault.vaultY, vault.vaultZ);

            info("前往目标坐标: {}, {}", vault.vaultX, vault.vaultZ);
            BARITONE.pathTo(targetGoal).addExecutedListener(f -> {
//                info("已到达目标坐标: {}, {}，开始搜索白桦木按钮", vault.vaultX, vault.vaultZ);
                info("已到达目标坐标: {}, {}，查找宝库", vault.vaultX, vault.vaultZ);
                reachedTargetPosition = true;
                scheduleAction(this::gotoLocation, 1); // 递归调用，进入按按钮逻辑
            });
            return;
        }
        searchForVault();

    }

    private boolean isBirchButton(int x, int y, int z) {
        int blockStateId = BlockStateInterface.getId(x, y, z);
        // 检查是否是白桦木按钮
        if (blockStateId >= BlockRegistry.BIRCH_BUTTON.minStateId() &&
                blockStateId <= BlockRegistry.BIRCH_BUTTON.maxStateId()) {
            return true;
        }

        // 检查是否是木按钮
        if (blockStateId >= BlockRegistry.OAK_BUTTON.minStateId() &&
                blockStateId <= BlockRegistry.OAK_BUTTON.maxStateId()) {
            return true;
        }

        // 检查是否是木按钮
        if (blockStateId >= BlockRegistry.CHERRY_BUTTON.minStateId() &&
                blockStateId <= BlockRegistry.CHERRY_BUTTON.maxStateId()) {
            return true;
        }

        return false;
    }

    private void pressBirchButton(int x, int y, int z) {
        if (!isRunning) return;
        AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
        AutoVaultOpenerConfig.VaultInfo vault = account.vaults.get(currentVaultIndex);

        info("按下配置的按钮坐标: {}, {}, {}", vault.buttonX, vault.buttonY, vault.buttonZ);

        currentState = State.GETTING_KEY; // 复用原状态

        BARITONE.rightClickBlock(vault.buttonX, vault.buttonY, vault.buttonZ);

        // 按下按钮后2秒进入开宝库流程
        scheduleAction(() -> openVault(x, y, z), 2);
    }

    private void rotateTo(int x, int y, int z) {
        var rotation = RotationHelper.shortestRotationTo(x, y, z);
        INPUTS.submit(InputRequest.builder()
                .owner(this)
                .yaw(rotation.getX())
                .pitch(rotation.getY())
                .priority(500)
                .build());
    }

    private boolean hasRotation(int x, int y, int z) {
        var entityRaycastResult = RaycastHelper.playerEyeRaycastThroughToBlockTarget(x, y, z);
        return entityRaycastResult.hit();
    }


    private void searchForShulker2() {
        if (!isRunning) return;
        info("搜索附近的潜影盒...");
        currentState = State.GOTO_LOCATION;

        // 搜索附近的潜影盒
        ChunkCache chunkCache = CACHE.getChunkCache();
        int playerX = (int) CACHE.getPlayerCache().getX();
        int playerY = (int) CACHE.getPlayerCache().getY();
        int playerZ = (int) CACHE.getPlayerCache().getZ();

        boolean foundShulker = false;

        // 在搜索范围内查找潜影盒
        for (int dx = -config.searchRadius; dx <= config.searchRadius; dx++) {
            for (int dy = -config.searchRadius; dy <= config.searchRadius; dy++) {
                for (int dz = -config.searchRadius; dz <= config.searchRadius; dz++) {
                    int x = playerX + dx;
                    int y = playerY + dy;
                    int z = playerZ + dz;

                    if (isShulkerBox(x, y, z)) {
                        info("找到潜影盒在坐标: {}, {}, {}", x, y, z);
                        openShulkerBox(x, y, z);
                        foundShulker = true;
                        break;
                    }
                }
                if (foundShulker) break;
            }
            if (foundShulker) break;
        }

        if (!foundShulker) {
            warn("未找到潜影盒");
            handleError("未找到潜影盒");
        }
    }

    private boolean isShulkerBox(int x, int y, int z) {
        ChunkCache chunkCache = CACHE.getChunkCache();
        int blockStateId = BlockStateInterface.getId(x, y, z);
        // 检查是否是潜影盒
        return blockStateId >= BlockRegistry.SHULKER_BOX.minStateId() &&
                blockStateId <= BlockRegistry.SHULKER_BOX.maxStateId();
    }

    private void openShulkerBox(int x, int y, int z) {
        if (!isRunning) return;
        info("打开潜影盒: {}, {}, {}", x, y, z);
        currentState = State.GETTING_KEY;

        // 发送右键点击包
        sendClientPacketAsync(new ServerboundUseItemOnPacket(
                x, y, z, Direction.UP, Hand.MAIN_HAND, 0, 0, 0, false, false, 0
        ));

        scheduleAction(this::takeKeyFromShulker, config.waitAfterShulkerOpen);
    }

    private void takeKeyFromShulker() {
        if (!isRunning) return;
        info("从潜影盒中取钥匙...");
        currentState = State.TAKING_KEY;

        // 检查打开的容器
        Container openContainer = CACHE.getPlayerCache().getInventoryCache().getOpenContainer();
        if (openContainer == null) {
            warn("没有打开的容器");
            handleError("没有打开的容器");
            return;
        }

        // 查找不详钥匙
        int keySlot = -1;
        for (int i = 0; i < openContainer.getContents().size(); i++) {
            ItemStack item = openContainer.getContents().get(i);
            if (item != null && isOminousKey(item)) {
                keySlot = i;
                break;
            }
        }

        if (keySlot == -1) {
            warn("未找到不详钥匙");
            handleError("未找到不详钥匙");
            return;
        }

        // 点击取钥匙
//        sendClientPacketAsync(new ServerboundContainerClickPacket(
//                openContainer.getContainerId(),
//                0, // state id
//                keySlot,
//                ContainerActionType.PICKUP_ITEM,
//                null,
//                null
//        ));

        // 关闭容器
        scheduleAction(() -> {
            sendClientPacketAsync(new ServerboundContainerClosePacket(openContainer.getContainerId()));
            scheduleAction(this::searchForVault, 1);
        }, 1);
    }

    private boolean isOminousKey(ItemStack item) {
        // 检查是否是不详钥匙
        // 不详钥匙通常是带有特定NBT的物品，这里使用一个通用的检查方法
        if (item == null) return false;

        ItemData itemData = ItemRegistry.REGISTRY.get(item.getId());
        return itemData != null && itemData == ItemRegistry.OMINOUS_TRIAL_KEY;

    }

    private void searchForVault() {
        if (!isRunning) return;
        info("搜索附近的宝库...");
        currentState = State.SEARCHING_VAULT;

        // 搜索附近的宝库
        int playerX = (int) CACHE.getPlayerCache().getX();
        int playerY = (int) CACHE.getPlayerCache().getY();
        int playerZ = (int) CACHE.getPlayerCache().getZ();

        boolean foundVault = false;

        // 在搜索范围内查找宝库
        for (int dx = -config.searchRadius; dx <= config.searchRadius; dx++) {
            for (int dy = -config.searchRadius; dy <= config.searchRadius; dy++) {
                for (int dz = -config.searchRadius; dz <= config.searchRadius; dz++) {
                    int x = playerX + dx;
                    int y = playerY + dy;
                    int z = playerZ + dz;

                    if (isVault(x, y, z)) {
                        info("找到宝库在坐标: {}, {}, {}", x, y, z);
                        checkVault(x, y, z);

                        foundVault = true;
                        break;
                    }
                }
                if (foundVault) break;
            }
            if (foundVault) break;
        }

        if (!foundVault) {
            warn("未找到宝库");
            handleError("未找到宝库");
        }
    }

    int checkVaultTime = 0;

    private void checkVault(int x, int y, int z) {
        if (checkVaultTime > 3) {
            info("宝库状态不可用！");
            checkVaultTime = 0;

            AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
            if (currentVaultIndex + 1 < account.vaults.size()) {
                info("宝库1操作完成，准备开下一个");
                currentVaultIndex++;
                reachedTargetPosition = false;
                currentState = State.WAITING_FOR_INGAME;
                gotoLocation();
            } else {
                info("宝库2操作完成，准备断开连接");
                currentState = State.DISCONNECTING;
                reachedTargetPosition = false;
                currentVaultIndex = 0;
                Proxy.getInstance().disconnect();
            }


            return;
        }
        if (isVaultActive(x, y, z)) {
            checkVaultTime = 0;

            pressBirchButtonWithDrop(x, y, z);

        } else {
            checkVaultTime++;
            scheduleAction(() -> checkVault(x, y, z), 1);
        }

    }

    private boolean isVault(int x, int y, int z) {
        int blockStateId = BlockStateInterface.getId(x, y, z);
        // 检查是否是宝库（这里需要根据实际的方块ID来判断）
        if (blockStateId >= BlockRegistry.VAULT.minStateId() &&
                blockStateId <= BlockRegistry.VAULT.maxStateId()) {


            var omini = World.getBlockStateProperty(blockStateId, BlockStateProperties.OMINOUS);
            if (omini != null) {
                return omini;
            }
        }

        return false;
    }

    private boolean isVaultActive(int x, int y, int z) {
        int blockStateId = BlockStateInterface.getId(x, y, z);
        // 检查是否是宝库（这里需要根据实际的方块ID来判断）
        if (blockStateId >= BlockRegistry.VAULT.minStateId() &&
                blockStateId <= BlockRegistry.VAULT.maxStateId()) {


            var omini = World.getBlockStateProperty(blockStateId, BlockStateProperties.OMINOUS);
            if (omini != null && omini) {
                VaultState state = World.getBlockStateProperty(blockStateId, BlockStateProperties.VAULT_STATE);

                return VaultState.ACTIVE.equals(state);
            }
        }

        return false;
    }

    private boolean isVaultOpen(int x, int y, int z) {
        int blockStateId = BlockStateInterface.getId(x, y, z);
        // 检查是否是宝库（这里需要根据实际的方块ID来判断）
        if (blockStateId >= BlockRegistry.VAULT.minStateId() &&
                blockStateId <= BlockRegistry.VAULT.maxStateId()) {


            var omini = World.getBlockStateProperty(blockStateId, BlockStateProperties.OMINOUS);
            if (omini != null && omini) {
                VaultState state = World.getBlockStateProperty(blockStateId, BlockStateProperties.VAULT_STATE);

                return !VaultState.ACTIVE.equals(state);
            }
        }

        return false;
    }

    private void openVault(int x, int y, int z) {
        if (!isRunning) return;
        currentState = State.OPENING_VAULT;

        // 参考 AutoOmen 的逻辑，在打开宝库前切换到钥匙
        if (switchToKey()) {
            // 如果成功切换到钥匙，等待切换完成后再打开宝库
            info("成功切换到钥匙");
            info("打开宝库: {}, {}, {}", x, y, z);
            BARITONE.rightClickBlock(x, y, z).addExecutedListener(e -> {
                scheduleAction(() -> {
                    if (isVaultOpen(x, y, z)) {
                        scheduleAction(this::waitForVault, 1);
                    } else {
                        scheduleAction(() -> {
                            scheduleAction(() -> openVault(x, y, z), 1);
                        }, config.waitAfterVaultOpen);
                    }

                }, 1);


            });

        } else {
            info("切换钥匙失败 重试中");
            scheduleAction(() -> openVault(x, y, z), 1);

            // 如果没有钥匙，直接尝试打开宝库
//            if (!hasRotation(x, y, z)) {
//                rotateTo(x, y, z);
//            }
//
//            var rotation = RotationHelper.shortestRotationTo(x, y, z);
//
//            INPUTS.submit(InputRequest.builder()
//                    .owner(this)
//                    .input(Input.builder()
//                            .rightClick(true)
//                            .clickTarget(new ClickTarget.BlockPosition(x, y, z))
//                            .build())
//                    .yaw(rotation.getX())
//                    .pitch(rotation.getY())
//                    .priority(500)
//                    .build());
//
//            scheduleAction(this::waitForVault, config.waitAfterVaultOpen);
        }
    }


    private void waitForVault() {
        if (!isRunning) return;
        info("等待宝库打开...");
        currentState = State.WAITING_VAULT;

        // 等待一段时间后断开连接
        scheduleAction(() -> {

            AutoVaultOpenerConfig.AccountInfo account = config.accounts.get(config.currentAccountIndex);
            if (currentVaultIndex + 1 < account.vaults.size()) {
                info("宝库1操作完成，准备开下一个");
                currentVaultIndex++;
                reachedTargetPosition = false;
                gotoLocation();
            } else {
                info("宝库2操作完成，准备断开连接");
                currentState = State.DISCONNECTING;
                reachedTargetPosition = false;

                Proxy.getInstance().disconnect();
            }


        }, config.waitAfterVaultOpen);
    }

    /**
     * 丢掉 hotbar 中的所有物品
     */
    private void dropAllHotbarItems() {
        if (!isRunning) return;
        info("丢掉 hotbar 中的所有物品...");
        
        Container playerInventory = CACHE.getPlayerCache().getInventoryCache().getPlayerInventory();
        
        // 遍历 hotbar 槽位 (36-44)
        for (int slot = 36; slot <= 44; slot++) {
            ItemStack item = playerInventory.getItemStack(slot);
            if (item != null && item != Container.EMPTY_STACK) {
                // 丢掉这个槽位的物品
                INVENTORY.submit(InventoryActionRequest.builder()
                    .owner(this)
                    .actions(new DropItem(slot, DropItemAction.DROP_SELECTED_STACK))
                    .priority(600)
                    .build());
            }
        }
    }

    /**
     * 在按下按钮前丢掉所有 hotbar 物品
     */
    private void pressBirchButtonWithDrop(int x, int y, int z) {
        if (!isRunning) return;
        
        // 先丢掉所有 hotbar 物品
        dropAllHotbarItems();
        
        // 等待1秒后按下按钮
        scheduleAction(() -> pressBirchButton(x, y, z), 1);
    }

    // 参考 AutoOmen 的 switchToFood 方法
    public boolean switchToKey() {
        delay = doInventoryActions();
        final boolean shouldStartUsing = getHand() != null && delay == 0;
        isUsingKey = getHand() != null || delay != 0;
        return shouldStartUsing;
    }

    private void handleError(String error) {
        error("发生错误: {}", error);
        retryCount++;

        if (retryCount >= config.maxRetries) {
            error("重试次数已达上限，跳过当前账号");
            retryCount = 0;
            config.currentAccountIndex++;
            scheduleAction(this::processNextAccount, config.retryDelaySeconds);
        } else {
            info("准备重试，当前重试次数: {}/{}", retryCount, config.maxRetries);
            scheduleAction(() -> {
                retryCount = 0;
                Proxy.getInstance().disconnect();
            }, config.retryDelaySeconds);
        }
    }

    private void scheduleAction(Runnable action, int delaySeconds) {
        if (nextActionFuture != null) {
            nextActionFuture.cancel(false);
        }

        nextActionFuture = EXECUTOR.schedule(() -> {
            if (isRunning) {
                lastActionTime = Instant.now();
                action.run();
            }
        }, delaySeconds, TimeUnit.SECONDS);
    }

    private void sendChatMessage(String message) {
        if (Proxy.getInstance().isConnected()) {
            Proxy.getInstance().getClient().sendAsync(new ServerboundChatPacket(message));
            info("发送聊天消息: {}", message);
        }
    }

    private void saveConfig() {
        // 触发配置保存
        saveConfigAsync();
        info("已保存当前进度：第 {}/{} 个账号", config.currentAccountIndex + 1, config.accounts.size());
    }
}
