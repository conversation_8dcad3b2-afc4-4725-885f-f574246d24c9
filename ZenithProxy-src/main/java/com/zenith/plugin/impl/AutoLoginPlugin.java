package com.zenith.plugin.impl;

import com.zenith.plugin.api.Plugin;
import com.zenith.plugin.api.PluginAPI;
import com.zenith.plugin.api.ZenithProxyPlugin;
import com.github.rfresh2.EventConsumer;
import com.zenith.event.chat.SystemChatEvent;
import org.geysermc.mcprotocollib.protocol.packet.ingame.serverbound.ServerboundChatPacket;
import com.zenith.Proxy;
import com.zenith.Globals;

import static com.github.rfresh2.EventConsumer.of;

@Plugin(
    id = "auto-login",
    version = "1.0.0",
    description = "自动登录插件 - 监听SystemChatEvent自动发送登录指令",
    authors = {"AI Assistant"}
)
public class AutoLoginPlugin implements ZenithProxyPlugin {
    private static final String LOGIN_COMMAND = "/login";
    private static final String PASSWORD = "your_password_here"; // TODO: 修改为实际密码

    @Override
    public void onLoad(PluginAPI pluginAPI) {
        Globals.EVENT_BUS.subscribe(this, of(SystemChatEvent.class, this::onSystemChat));
        pluginAPI.getLogger().info("自动登录插件已加载");
    }

    private void onSystemChat(SystemChatEvent event) {
        String message = event.message();
        if (message.contains("/login")) {
            sendChatMessage(LOGIN_COMMAND + " " + PASSWORD);
        }
    }

    private void sendChatMessage(String message) {
        if (Proxy.getInstance().isConnected()) {
            Proxy.getInstance().getClient().sendAsync(new ServerboundChatPacket(message));
        }
    }
} 