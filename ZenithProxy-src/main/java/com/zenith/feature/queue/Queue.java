package com.zenith.feature.queue;

import com.zenith.Proxy;
import com.zenith.feature.api.vcapi.VcApi;
import com.zenith.feature.api.vcapi.model.QueueEtaEquationResponse;
import com.zenith.feature.queue.mcping.MCPing;
import com.zenith.feature.queue.mcping.data.MCResponse;
import lombok.Locked;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.zenith.Globals.*;

public class Queue {
    private static QueueStatus queueStatus = new QueueStatus(0, 0, 0);
    private static final Pattern digitPattern = Pattern.compile("\\d+");
    private volatile static Instant lastUpdate = Instant.EPOCH;
    private static QueueEtaEquationResponse queueEtaEquation = new QueueEtaEquationResponse(199.0, 0.840);
    private static Instant lastQueueEtaEquationUpdate = Instant.EPOCH;
    private static Instant lastQueueApiUpdate = Instant.EPOCH;

    public static void start() {
        EXECUTOR.scheduleAtFixedRate(
            () -> Thread.ofVirtual().name("Queue Update").start(Queue::updateQueueStatus),
            1,
            CONFIG.server.queueStatusRefreshMinutes,
            TimeUnit.MINUTES);
        EXECUTOR.scheduleAtFixedRate(
            () -> Thread.ofVirtual().name("Queue ETA Update").start(Queue::updateQueueEtaEquation),
            1,
            60,
            TimeUnit.MINUTES
        );
    }

    public static QueueStatus getQueueStatus() {
        if (lastUpdate == Instant.EPOCH) {
            updateQueueStatusNow();
        }
        return queueStatus;
    }

    public static void updateQueueStatus() {
        // prevent certain situations where users get a ton of these requests queued up somehow
        // maybe due to losing internet?
        if (lastUpdate.isAfter(Instant.now().minus(Duration.ofMinutes(CONFIG.server.queueStatusRefreshMinutes)))) return;
        updateQueueStatusNow();
    }

    @Locked
    public static void updateQueueStatusNow() {
        if (lastUpdate.isAfter(Instant.now().minus(Duration.ofSeconds(5)))) return; // avoid getting rate limited by tcpshield
        lastUpdate = Instant.now();
        if (!pingUpdate()) {
            if (!apiUpdate()) {
                SERVER_LOG.debug("Failed updating queue status. Is the network down?");
            }
        }
    }

    // returns seconds until estimated queue completion time
    public static long getQueueWait(final Integer queuePos) {
        if (lastQueueEtaEquationUpdate == Instant.EPOCH) {
            updateQueueEtaEquation();
        }
        return (long) (queueEtaEquation.factor() * (Math.pow(queuePos.doubleValue(), queueEtaEquation.pow())));
    }

    public static String getEtaStringFromSeconds(final long totalSeconds) {
        final int hour = (int) (totalSeconds / 3600);
        final int minutes = (int) ((totalSeconds / 60) % 60);
        final int seconds = (int) (totalSeconds % 60);
        final String hourStr = hour >= 10 ? "" + hour : "0" + hour;
        final String minutesStr = minutes >= 10 ? "" + minutes : "0" + minutes;
        final String secondsStr = seconds >= 10 ? "" + seconds : "0" + seconds;
        return hourStr + ":" + minutesStr + ":" + secondsStr;
    }

    public static String getQueueEta(final Integer queuePos) {
        return getEtaStringFromSeconds(getQueueWait(queuePos));
    }

    public static boolean pingUpdate() {
        try {
            final MCResponse pingWithDetails = MCPing.INSTANCE.ping("connect.2b2t.org", 25565, 3000, false);
            final String queueStr = pingWithDetails.players().sample().get(1).name();
            final Matcher regularQMatcher = digitPattern.matcher(queueStr.substring(queueStr.lastIndexOf(" ")));
            final String prioQueueStr = pingWithDetails.players().sample().get(2).name();
            final Matcher prioQMatcher = digitPattern.matcher(prioQueueStr.substring(prioQueueStr.lastIndexOf(" ")));
            if (!queueStr.contains("Queue")) {
                throw new IOException("Queue string doesn't contain Queue: " + queueStr);
            }
            if (!prioQueueStr.contains("Priority")) {
                throw new IOException("Priority queue string doesn't contain Priority: " + prioQueueStr);
            }
            if (!regularQMatcher.find()) {
                throw new IOException("didn't find regular queue len: " + queueStr);
            }
            if (!prioQMatcher.find()) {
                throw new IOException("didn't find priority queue len: " + prioQueueStr);
            }
            final int regular = Integer.parseInt(regularQMatcher.group());
            final int prio = Integer.parseInt(prioQMatcher.group());
            queueStatus = new QueueStatus(prio, regular, ZonedDateTime.now().toEpochSecond());
            return true;
        } catch (final Exception e) {
            SERVER_LOG.debug("Failed updating queue with ping. 2b2t is likely offline", e);
            return false;
        }
    }

    private static boolean apiUpdate() {
        if (lastQueueApiUpdate.isAfter(Instant.now().minus(Duration.ofMinutes(5)))) return true;
        lastQueueApiUpdate = Instant.now();
        try {
            var response = VcApi.INSTANCE.getQueue().orElseThrow();
            queueStatus = new QueueStatus(response.prio(), response.regular(), response.time().toEpochSecond());
            return true;
        } catch (final Exception e) {
            SERVER_LOG.debug("Failed updating queue status from API", e);
            return false;
        }
    }

    @Locked
    public static void updateQueueEtaEquation() {
        if (!CONFIG.server.dynamicQueueEtaEquation) return;
        if (lastQueueEtaEquationUpdate.isAfter(Instant.now().minus(Duration.ofHours(1)))) return;
        try {
            queueEtaEquation = VcApi.INSTANCE.getQueueEtaEquation().orElseThrow();
            lastQueueEtaEquationUpdate = Instant.now();
        } catch (final Exception e) {
            var nextWaitMinutes = ThreadLocalRandom.current().nextInt(1, 6);
            SERVER_LOG.debug("Error fetching queue ETA equation. Retrying in {} minutes", nextWaitMinutes, e);
            EXECUTOR.schedule(
                Queue::updateQueueEtaEquation,
                nextWaitMinutes,
                TimeUnit.MINUTES
            );
        }
    }

    public static String queuePositionStr() {
        if (Proxy.getInstance().isPrio())
            return Proxy.getInstance().getQueuePosition() + " / " + getQueueStatus().prio() + " - ETA: " + getQueueEta(Proxy.getInstance().getQueuePosition());
        else
            return Proxy.getInstance().getQueuePosition() + " / " + getQueueStatus().regular() + " - ETA: " + getQueueEta(Proxy.getInstance().getQueuePosition());
    }
}
