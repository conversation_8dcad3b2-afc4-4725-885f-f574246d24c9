package com.zenith.mc.block.properties;

import com.zenith.mc.block.properties.api.StringRepresentable;

public enum ChestType implements StringRepresentable {
   SINGLE("single"),
   LEFT("left"),
   RIGHT("right");

   private final String name;

   private ChestType(final String name) {
      this.name = name;
   }

   @Override
   public String getSerializedName() {
      return this.name;
   }

   public ChestType getOpposite() {
      return switch (this) {
         case SINGLE -> SINGLE;
         case LEFT -> RIGHT;
         case RIGHT -> LEFT;
      };
   }
}
