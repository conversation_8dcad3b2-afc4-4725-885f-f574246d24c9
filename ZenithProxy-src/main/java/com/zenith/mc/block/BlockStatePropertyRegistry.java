// Auto-Generated by ZenithProxy Data Generator
package com.zenith.mc.block;

import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;

import static com.zenith.mc.block.properties.api.BlockStateProperties.*;

public final class BlockStatePropertyRegistry {
    public static final Int2ObjectMap<BlockStatePropertyDefinition> STATES = new Int2ObjectOpenHashMap<>(1095);

    static {
        STATES.put(8, new BlockStatePropertyDefinition(SNOWY));
        STATES.put(11, new BlockStatePropertyDefinition(SNOWY));
        STATES.put(20, new BlockStatePropertyDefinition(AXIS));
        STATES.put(25, new BlockStatePropertyDefinition(STAGE));
        STATES.put(26, new BlockStatePropertyDefinition(STAGE));
        STATES.put(27, new BlockStatePropertyDefinition(STAGE));
        STATES.put(28, new BlockStatePropertyDefinition(STAGE));
        STATES.put(29, new BlockStatePropertyDefinition(STAGE));
        STATES.put(30, new BlockStatePropertyDefinition(STAGE));
        STATES.put(31, new BlockStatePropertyDefinition(STAGE));
        STATES.put(32, new BlockStatePropertyDefinition(STAGE));
        STATES.put(33, new BlockStatePropertyDefinition(AGE_4, HANGING, STAGE, WATERLOGGED));
        STATES.put(35, new BlockStatePropertyDefinition(LEVEL));
        STATES.put(36, new BlockStatePropertyDefinition(LEVEL));
        STATES.put(38, new BlockStatePropertyDefinition(DUSTED));
        STATES.put(41, new BlockStatePropertyDefinition(DUSTED));
        STATES.put(49, new BlockStatePropertyDefinition(AXIS));
        STATES.put(50, new BlockStatePropertyDefinition(AXIS));
        STATES.put(51, new BlockStatePropertyDefinition(AXIS));
        STATES.put(52, new BlockStatePropertyDefinition(AXIS));
        STATES.put(53, new BlockStatePropertyDefinition(AXIS));
        STATES.put(54, new BlockStatePropertyDefinition(AXIS));
        STATES.put(55, new BlockStatePropertyDefinition(AXIS));
        STATES.put(56, new BlockStatePropertyDefinition(AXIS));
        STATES.put(57, new BlockStatePropertyDefinition(AXIS));
        STATES.put(58, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(59, new BlockStatePropertyDefinition(AXIS));
        STATES.put(60, new BlockStatePropertyDefinition(AXIS));
        STATES.put(61, new BlockStatePropertyDefinition(AXIS));
        STATES.put(62, new BlockStatePropertyDefinition(AXIS));
        STATES.put(63, new BlockStatePropertyDefinition(AXIS));
        STATES.put(64, new BlockStatePropertyDefinition(AXIS));
        STATES.put(65, new BlockStatePropertyDefinition(AXIS));
        STATES.put(66, new BlockStatePropertyDefinition(AXIS));
        STATES.put(67, new BlockStatePropertyDefinition(AXIS));
        STATES.put(68, new BlockStatePropertyDefinition(AXIS));
        STATES.put(69, new BlockStatePropertyDefinition(AXIS));
        STATES.put(70, new BlockStatePropertyDefinition(AXIS));
        STATES.put(71, new BlockStatePropertyDefinition(AXIS));
        STATES.put(72, new BlockStatePropertyDefinition(AXIS));
        STATES.put(73, new BlockStatePropertyDefinition(AXIS));
        STATES.put(74, new BlockStatePropertyDefinition(AXIS));
        STATES.put(75, new BlockStatePropertyDefinition(AXIS));
        STATES.put(76, new BlockStatePropertyDefinition(AXIS));
        STATES.put(77, new BlockStatePropertyDefinition(AXIS));
        STATES.put(78, new BlockStatePropertyDefinition(AXIS));
        STATES.put(79, new BlockStatePropertyDefinition(AXIS));
        STATES.put(80, new BlockStatePropertyDefinition(AXIS));
        STATES.put(81, new BlockStatePropertyDefinition(AXIS));
        STATES.put(82, new BlockStatePropertyDefinition(AXIS));
        STATES.put(83, new BlockStatePropertyDefinition(AXIS));
        STATES.put(84, new BlockStatePropertyDefinition(AXIS));
        STATES.put(85, new BlockStatePropertyDefinition(AXIS));
        STATES.put(86, new BlockStatePropertyDefinition(AXIS));
        STATES.put(87, new BlockStatePropertyDefinition(AXIS));
        STATES.put(88, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(89, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(90, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(91, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(92, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(93, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(94, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(95, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(96, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(97, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(98, new BlockStatePropertyDefinition(DISTANCE, PERSISTENT, WATERLOGGED));
        STATES.put(105, new BlockStatePropertyDefinition(FACING, TRIGGERED));
        STATES.put(109, new BlockStatePropertyDefinition(NOTEBLOCK_INSTRUMENT, NOTE, POWERED));
        STATES.put(110, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(111, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(112, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(113, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(114, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(115, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(116, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(117, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(118, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(119, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(120, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(121, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(122, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(123, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(124, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(125, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OCCUPIED, BED_PART));
        STATES.put(126, new BlockStatePropertyDefinition(POWERED, RAIL_SHAPE_STRAIGHT, WATERLOGGED));
        STATES.put(127, new BlockStatePropertyDefinition(POWERED, RAIL_SHAPE_STRAIGHT, WATERLOGGED));
        STATES.put(128, new BlockStatePropertyDefinition(EXTENDED, FACING));
        STATES.put(134, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(135, new BlockStatePropertyDefinition(EXTENDED, FACING));
        STATES.put(136, new BlockStatePropertyDefinition(FACING, SHORT, PISTON_TYPE));
        STATES.put(153, new BlockStatePropertyDefinition(FACING, PISTON_TYPE));
        STATES.put(173, new BlockStatePropertyDefinition(UNSTABLE));
        STATES.put(175, new BlockStatePropertyDefinition(HORIZONTAL_FACING, CHISELED_BOOKSHELF_SLOT_0_OCCUPIED, CHISELED_BOOKSHELF_SLOT_1_OCCUPIED, CHISELED_BOOKSHELF_SLOT_2_OCCUPIED, CHISELED_BOOKSHELF_SLOT_3_OCCUPIED, CHISELED_BOOKSHELF_SLOT_4_OCCUPIED, CHISELED_BOOKSHELF_SLOT_5_OCCUPIED));
        STATES.put(179, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(180, new BlockStatePropertyDefinition(AGE_15, EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(183, new BlockStatePropertyDefinition(ACTIVE, AXIS, NATURAL));
        STATES.put(184, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(185, new BlockStatePropertyDefinition(HORIZONTAL_FACING, CHEST_TYPE, WATERLOGGED));
        STATES.put(186, new BlockStatePropertyDefinition(EAST_REDSTONE, NORTH_REDSTONE, POWER, SOUTH_REDSTONE, WEST_REDSTONE));
        STATES.put(191, new BlockStatePropertyDefinition(AGE_7));
        STATES.put(192, new BlockStatePropertyDefinition(MOISTURE));
        STATES.put(193, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT));
        STATES.put(194, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(195, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(196, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(197, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(198, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(199, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(200, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(201, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(202, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(203, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(204, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(205, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(206, new BlockStatePropertyDefinition(RAIL_SHAPE, WATERLOGGED));
        STATES.put(207, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(208, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(209, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(210, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(211, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(212, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(213, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(214, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(215, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(216, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(217, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(218, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(219, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(220, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(221, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(222, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(223, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(224, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(225, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(226, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(227, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(228, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(229, new BlockStatePropertyDefinition(ATTACHED, ROTATION_16, WATERLOGGED));
        STATES.put(230, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(231, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(232, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(233, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(234, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(235, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(236, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(237, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(238, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(239, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(240, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(241, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(242, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(243, new BlockStatePropertyDefinition(POWERED));
        STATES.put(244, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(245, new BlockStatePropertyDefinition(POWERED));
        STATES.put(246, new BlockStatePropertyDefinition(POWERED));
        STATES.put(247, new BlockStatePropertyDefinition(POWERED));
        STATES.put(248, new BlockStatePropertyDefinition(POWERED));
        STATES.put(249, new BlockStatePropertyDefinition(POWERED));
        STATES.put(250, new BlockStatePropertyDefinition(POWERED));
        STATES.put(251, new BlockStatePropertyDefinition(POWERED));
        STATES.put(252, new BlockStatePropertyDefinition(POWERED));
        STATES.put(253, new BlockStatePropertyDefinition(POWERED));
        STATES.put(254, new BlockStatePropertyDefinition(POWERED));
        STATES.put(255, new BlockStatePropertyDefinition(LIT));
        STATES.put(256, new BlockStatePropertyDefinition(LIT));
        STATES.put(257, new BlockStatePropertyDefinition(LIT));
        STATES.put(258, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT));
        STATES.put(259, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(260, new BlockStatePropertyDefinition(LAYERS));
        STATES.put(263, new BlockStatePropertyDefinition(AGE_15));
        STATES.put(265, new BlockStatePropertyDefinition(AGE_15));
        STATES.put(266, new BlockStatePropertyDefinition(HAS_RECORD));
        STATES.put(267, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(271, new BlockStatePropertyDefinition(AXIS));
        STATES.put(272, new BlockStatePropertyDefinition(AXIS));
        STATES.put(274, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(276, new BlockStatePropertyDefinition(HORIZONTAL_AXIS));
        STATES.put(277, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(278, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(279, new BlockStatePropertyDefinition(BITES));
        STATES.put(280, new BlockStatePropertyDefinition(DELAY, HORIZONTAL_FACING, LOCKED, POWERED));
        STATES.put(297, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(298, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(299, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(300, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(301, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(302, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(303, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(304, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(305, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(306, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(319, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(320, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(321, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(322, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(323, new BlockStatePropertyDefinition(AXIS, WATERLOGGED));
        STATES.put(324, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(327, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(328, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(329, new BlockStatePropertyDefinition(AGE_7));
        STATES.put(330, new BlockStatePropertyDefinition(AGE_7));
        STATES.put(331, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(332, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WATERLOGGED, WEST));
        STATES.put(333, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WATERLOGGED, WEST));
        STATES.put(334, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(335, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(336, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(337, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(338, new BlockStatePropertyDefinition(SNOWY));
        STATES.put(342, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(343, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(344, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(347, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(348, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(349, new BlockStatePropertyDefinition(AGE_3));
        STATES.put(351, new BlockStatePropertyDefinition(HAS_BOTTLE_0, HAS_BOTTLE_1, HAS_BOTTLE_2));
        STATES.put(353, new BlockStatePropertyDefinition(LEVEL_CAULDRON));
        STATES.put(355, new BlockStatePropertyDefinition(LEVEL_CAULDRON));
        STATES.put(357, new BlockStatePropertyDefinition(EYE, HORIZONTAL_FACING));
        STATES.put(360, new BlockStatePropertyDefinition(LIT));
        STATES.put(361, new BlockStatePropertyDefinition(AGE_2, HORIZONTAL_FACING));
        STATES.put(362, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(365, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(366, new BlockStatePropertyDefinition(ATTACHED, HORIZONTAL_FACING, POWERED));
        STATES.put(367, new BlockStatePropertyDefinition(ATTACHED, DISARMED, EAST, NORTH, POWERED, SOUTH, WEST));
        STATES.put(369, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(370, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(371, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(372, new BlockStatePropertyDefinition(CONDITIONAL, FACING));
        STATES.put(374, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(375, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(405, new BlockStatePropertyDefinition(AGE_7));
        STATES.put(406, new BlockStatePropertyDefinition(AGE_7));
        STATES.put(407, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(408, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(409, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(410, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(411, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(412, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(413, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(414, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(415, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(416, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(417, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(418, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(419, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(420, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(421, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(422, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(423, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(424, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(425, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(426, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(427, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(428, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(429, new BlockStatePropertyDefinition(POWERED, ROTATION_16));
        STATES.put(430, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWERED));
        STATES.put(431, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(432, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(433, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(434, new BlockStatePropertyDefinition(HORIZONTAL_FACING, CHEST_TYPE, WATERLOGGED));
        STATES.put(435, new BlockStatePropertyDefinition(POWER));
        STATES.put(436, new BlockStatePropertyDefinition(POWER));
        STATES.put(437, new BlockStatePropertyDefinition(HORIZONTAL_FACING, MODE_COMPARATOR, POWERED));
        STATES.put(438, new BlockStatePropertyDefinition(INVERTED, POWER));
        STATES.put(441, new BlockStatePropertyDefinition(ENABLED, FACING_HOPPER));
        STATES.put(444, new BlockStatePropertyDefinition(AXIS));
        STATES.put(445, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(446, new BlockStatePropertyDefinition(POWERED, RAIL_SHAPE_STRAIGHT, WATERLOGGED));
        STATES.put(447, new BlockStatePropertyDefinition(FACING, TRIGGERED));
        STATES.put(464, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(465, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(466, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(467, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(468, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(469, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(470, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(471, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(472, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(473, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(474, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(475, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(476, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(477, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(478, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(479, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(480, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(481, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(482, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(483, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(484, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(485, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(486, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(488, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(489, new BlockStatePropertyDefinition(LEVEL, WATERLOGGED));
        STATES.put(490, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(494, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(495, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(496, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(497, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(498, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(499, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(501, new BlockStatePropertyDefinition(AXIS));
        STATES.put(521, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(522, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(523, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(524, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(525, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(526, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(527, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(528, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(529, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(530, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(531, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(532, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(533, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(534, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(535, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(536, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(537, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(538, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(539, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(540, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(541, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(542, new BlockStatePropertyDefinition(ROTATION_16));
        STATES.put(543, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(544, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(545, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(546, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(547, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(548, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(549, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(550, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(551, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(552, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(553, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(554, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(555, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(556, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(557, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(558, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(562, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(563, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(564, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(565, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(566, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(567, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(568, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(569, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(570, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(571, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(572, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(573, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(574, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(575, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(576, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(577, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(578, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(579, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(580, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(581, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(582, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(583, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(584, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(585, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(586, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(587, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(592, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(593, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(594, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(595, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(596, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(597, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(598, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(599, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(600, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(601, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(602, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(603, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(604, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(605, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(606, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(607, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(608, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(609, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(610, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(611, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(612, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(613, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(614, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(615, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(616, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(617, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(618, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(619, new BlockStatePropertyDefinition(FACING));
        STATES.put(620, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WEST));
        STATES.put(621, new BlockStatePropertyDefinition(AGE_5));
        STATES.put(623, new BlockStatePropertyDefinition(AXIS));
        STATES.put(624, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(626, new BlockStatePropertyDefinition(AGE_1));
        STATES.put(627, new BlockStatePropertyDefinition(AGE_4, DOUBLE_BLOCK_HALF));
        STATES.put(628, new BlockStatePropertyDefinition(DOUBLE_BLOCK_HALF));
        STATES.put(629, new BlockStatePropertyDefinition(AGE_3));
        STATES.put(632, new BlockStatePropertyDefinition(CONDITIONAL, FACING));
        STATES.put(633, new BlockStatePropertyDefinition(CONDITIONAL, FACING));
        STATES.put(634, new BlockStatePropertyDefinition(AGE_3));
        STATES.put(638, new BlockStatePropertyDefinition(AXIS));
        STATES.put(640, new BlockStatePropertyDefinition(FACING, POWERED));
        STATES.put(641, new BlockStatePropertyDefinition(FACING));
        STATES.put(642, new BlockStatePropertyDefinition(FACING));
        STATES.put(643, new BlockStatePropertyDefinition(FACING));
        STATES.put(644, new BlockStatePropertyDefinition(FACING));
        STATES.put(645, new BlockStatePropertyDefinition(FACING));
        STATES.put(646, new BlockStatePropertyDefinition(FACING));
        STATES.put(647, new BlockStatePropertyDefinition(FACING));
        STATES.put(648, new BlockStatePropertyDefinition(FACING));
        STATES.put(649, new BlockStatePropertyDefinition(FACING));
        STATES.put(650, new BlockStatePropertyDefinition(FACING));
        STATES.put(651, new BlockStatePropertyDefinition(FACING));
        STATES.put(652, new BlockStatePropertyDefinition(FACING));
        STATES.put(653, new BlockStatePropertyDefinition(FACING));
        STATES.put(654, new BlockStatePropertyDefinition(FACING));
        STATES.put(655, new BlockStatePropertyDefinition(FACING));
        STATES.put(656, new BlockStatePropertyDefinition(FACING));
        STATES.put(657, new BlockStatePropertyDefinition(FACING));
        STATES.put(658, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(659, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(660, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(661, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(662, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(663, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(664, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(665, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(666, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(667, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(668, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(669, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(670, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(671, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(672, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(673, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(706, new BlockStatePropertyDefinition(AGE_25));
        STATES.put(709, new BlockStatePropertyDefinition(EGGS, HATCH));
        STATES.put(710, new BlockStatePropertyDefinition(HATCH));
        STATES.put(721, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(722, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(723, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(724, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(725, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(726, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(727, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(728, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(729, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(730, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(731, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(732, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(733, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(734, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(735, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(736, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(737, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(738, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(739, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(740, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(741, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(742, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(743, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(744, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(745, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(746, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(747, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(748, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(749, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(750, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(751, new BlockStatePropertyDefinition(PICKLES, WATERLOGGED));
        STATES.put(753, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(755, new BlockStatePropertyDefinition(AGE_1, BAMBOO_LEAVES, STAGE));
        STATES.put(759, new BlockStatePropertyDefinition(DRAG));
        STATES.put(760, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(761, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(762, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(763, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(764, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(765, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(766, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(767, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(768, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(769, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(770, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(771, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(772, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(773, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(774, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(775, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(776, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(777, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(778, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(779, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(780, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(781, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(782, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(783, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(784, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(785, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(786, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(787, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(788, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(789, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(790, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(791, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(792, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(793, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(794, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(795, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(796, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(797, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(798, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(799, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(800, new BlockStatePropertyDefinition(BOTTOM, STABILITY_DISTANCE, WATERLOGGED));
        STATES.put(801, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(802, new BlockStatePropertyDefinition(FACING, OPEN));
        STATES.put(803, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT));
        STATES.put(804, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT));
        STATES.put(807, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING));
        STATES.put(808, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HAS_BOOK, POWERED));
        STATES.put(810, new BlockStatePropertyDefinition(HORIZONTAL_FACING));
        STATES.put(811, new BlockStatePropertyDefinition(BELL_ATTACHMENT, HORIZONTAL_FACING, POWERED));
        STATES.put(812, new BlockStatePropertyDefinition(HANGING, WATERLOGGED));
        STATES.put(813, new BlockStatePropertyDefinition(HANGING, WATERLOGGED));
        STATES.put(814, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT, SIGNAL_FIRE, WATERLOGGED));
        STATES.put(815, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LIT, SIGNAL_FIRE, WATERLOGGED));
        STATES.put(816, new BlockStatePropertyDefinition(AGE_3));
        STATES.put(817, new BlockStatePropertyDefinition(AXIS));
        STATES.put(818, new BlockStatePropertyDefinition(AXIS));
        STATES.put(819, new BlockStatePropertyDefinition(AXIS));
        STATES.put(820, new BlockStatePropertyDefinition(AXIS));
        STATES.put(826, new BlockStatePropertyDefinition(AXIS));
        STATES.put(827, new BlockStatePropertyDefinition(AXIS));
        STATES.put(828, new BlockStatePropertyDefinition(AXIS));
        STATES.put(829, new BlockStatePropertyDefinition(AXIS));
        STATES.put(833, new BlockStatePropertyDefinition(AGE_25));
        STATES.put(835, new BlockStatePropertyDefinition(AGE_25));
        STATES.put(840, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(841, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(842, new BlockStatePropertyDefinition(POWERED));
        STATES.put(843, new BlockStatePropertyDefinition(POWERED));
        STATES.put(844, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(845, new BlockStatePropertyDefinition(EAST, NORTH, SOUTH, WATERLOGGED, WEST));
        STATES.put(846, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(847, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(848, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(849, new BlockStatePropertyDefinition(HORIZONTAL_FACING, IN_WALL, OPEN, POWERED));
        STATES.put(850, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(851, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(852, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(853, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(854, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(855, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(856, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(857, new BlockStatePropertyDefinition(ROTATION_16, WATERLOGGED));
        STATES.put(858, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(859, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(860, new BlockStatePropertyDefinition(STRUCTUREBLOCK_MODE));
        STATES.put(861, new BlockStatePropertyDefinition(ORIENTATION));
        STATES.put(862, new BlockStatePropertyDefinition(LEVEL_COMPOSTER));
        STATES.put(863, new BlockStatePropertyDefinition(POWER));
        STATES.put(864, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LEVEL_HONEY));
        STATES.put(865, new BlockStatePropertyDefinition(HORIZONTAL_FACING, LEVEL_HONEY));
        STATES.put(871, new BlockStatePropertyDefinition(RESPAWN_ANCHOR_CHARGES));
        STATES.put(878, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(879, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(880, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(885, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(886, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(887, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(889, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(890, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(891, new BlockStatePropertyDefinition(POWERED));
        STATES.put(892, new BlockStatePropertyDefinition(ATTACH_FACE, HORIZONTAL_FACING, POWERED));
        STATES.put(893, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(897, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(898, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(899, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(900, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(901, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(902, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(903, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(904, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(905, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(906, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(907, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(908, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(909, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(910, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(911, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(912, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(913, new BlockStatePropertyDefinition(CANDLES, LIT, WATERLOGGED));
        STATES.put(914, new BlockStatePropertyDefinition(LIT));
        STATES.put(915, new BlockStatePropertyDefinition(LIT));
        STATES.put(916, new BlockStatePropertyDefinition(LIT));
        STATES.put(917, new BlockStatePropertyDefinition(LIT));
        STATES.put(918, new BlockStatePropertyDefinition(LIT));
        STATES.put(919, new BlockStatePropertyDefinition(LIT));
        STATES.put(920, new BlockStatePropertyDefinition(LIT));
        STATES.put(921, new BlockStatePropertyDefinition(LIT));
        STATES.put(922, new BlockStatePropertyDefinition(LIT));
        STATES.put(923, new BlockStatePropertyDefinition(LIT));
        STATES.put(924, new BlockStatePropertyDefinition(LIT));
        STATES.put(925, new BlockStatePropertyDefinition(LIT));
        STATES.put(926, new BlockStatePropertyDefinition(LIT));
        STATES.put(927, new BlockStatePropertyDefinition(LIT));
        STATES.put(928, new BlockStatePropertyDefinition(LIT));
        STATES.put(929, new BlockStatePropertyDefinition(LIT));
        STATES.put(930, new BlockStatePropertyDefinition(LIT));
        STATES.put(933, new BlockStatePropertyDefinition(FACING, WATERLOGGED));
        STATES.put(934, new BlockStatePropertyDefinition(FACING, WATERLOGGED));
        STATES.put(935, new BlockStatePropertyDefinition(FACING, WATERLOGGED));
        STATES.put(936, new BlockStatePropertyDefinition(FACING, WATERLOGGED));
        STATES.put(938, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(939, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(940, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(942, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(943, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(944, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(947, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(948, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(949, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(954, new BlockStatePropertyDefinition(POWER, SCULK_SENSOR_PHASE, WATERLOGGED));
        STATES.put(955, new BlockStatePropertyDefinition(HORIZONTAL_FACING, POWER, SCULK_SENSOR_PHASE, WATERLOGGED));
        STATES.put(957, new BlockStatePropertyDefinition(DOWN, EAST, NORTH, SOUTH, UP, WATERLOGGED, WEST));
        STATES.put(958, new BlockStatePropertyDefinition(BLOOM));
        STATES.put(959, new BlockStatePropertyDefinition(CAN_SUMMON, SHRIEKING, WATERLOGGED));
        STATES.put(978, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(979, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(980, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(981, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(982, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(983, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(984, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(985, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(994, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(995, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(996, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(997, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(998, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(999, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1000, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1001, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1002, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1003, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1004, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1005, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1006, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1007, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1008, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1009, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, DOOR_HINGE, OPEN, POWERED));
        STATES.put(1010, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1011, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1012, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1013, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1014, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1015, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1016, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1017, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, OPEN, POWERED, WATERLOGGED));
        STATES.put(1018, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1019, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1020, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1021, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1022, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1023, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1024, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1025, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1026, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1027, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1028, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1029, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1030, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1031, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1032, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1033, new BlockStatePropertyDefinition(LIT, POWERED));
        STATES.put(1034, new BlockStatePropertyDefinition(FACING, POWERED, WATERLOGGED));
        STATES.put(1035, new BlockStatePropertyDefinition(DRIPSTONE_THICKNESS, VERTICAL_DIRECTION, WATERLOGGED));
        STATES.put(1037, new BlockStatePropertyDefinition(AGE_25, BERRIES));
        STATES.put(1038, new BlockStatePropertyDefinition(BERRIES));
        STATES.put(1043, new BlockStatePropertyDefinition(HORIZONTAL_FACING, FLOWER_AMOUNT));
        STATES.put(1045, new BlockStatePropertyDefinition(HORIZONTAL_FACING, TILT, WATERLOGGED));
        STATES.put(1046, new BlockStatePropertyDefinition(HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(1047, new BlockStatePropertyDefinition(HORIZONTAL_FACING, DOUBLE_BLOCK_HALF, WATERLOGGED));
        STATES.put(1048, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1051, new BlockStatePropertyDefinition(AXIS));
        STATES.put(1053, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(1054, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1055, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(1057, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(1058, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1059, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(1061, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(1062, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1063, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(1065, new BlockStatePropertyDefinition(HORIZONTAL_FACING, HALF, STAIRS_SHAPE, WATERLOGGED));
        STATES.put(1066, new BlockStatePropertyDefinition(SLAB_TYPE, WATERLOGGED));
        STATES.put(1067, new BlockStatePropertyDefinition(EAST_WALL, NORTH_WALL, SOUTH_WALL, UP, WATERLOGGED, WEST_WALL));
        STATES.put(1071, new BlockStatePropertyDefinition(AXIS));
        STATES.put(1078, new BlockStatePropertyDefinition(AXIS));
        STATES.put(1079, new BlockStatePropertyDefinition(AXIS));
        STATES.put(1080, new BlockStatePropertyDefinition(AXIS));
        STATES.put(1083, new BlockStatePropertyDefinition(CRACKED, HORIZONTAL_FACING, WATERLOGGED));
        STATES.put(1084, new BlockStatePropertyDefinition(CRAFTING, ORIENTATION, TRIGGERED));
        STATES.put(1085, new BlockStatePropertyDefinition(OMINOUS, TRIAL_SPAWNER_STATE));
        STATES.put(1086, new BlockStatePropertyDefinition(HORIZONTAL_FACING, OMINOUS, VAULT_STATE));
        STATES.put(1087, new BlockStatePropertyDefinition(WATERLOGGED));
        STATES.put(1089, new BlockStatePropertyDefinition(BOTTOM, EAST_WALL, NORTH_WALL, SOUTH_WALL, WEST_WALL));
        STATES.put(1090, new BlockStatePropertyDefinition(TIP));
    }
}
