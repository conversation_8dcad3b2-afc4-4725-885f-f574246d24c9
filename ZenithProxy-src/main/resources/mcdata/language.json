{"advancement.advancementNotFound": "Unknown advancement: {0}", "advancements.adventure.adventuring_time.description": "Discover every biome", "advancements.adventure.adventuring_time.title": "Adventuring Time", "advancements.adventure.arbalistic.description": "Kill five unique mobs with one crossbow shot", "advancements.adventure.arbalistic.title": "Arbalistic", "advancements.adventure.avoid_vibration.description": "Sneak near a Sculk Sensor or Warden to prevent it from detecting you", "advancements.adventure.avoid_vibration.title": "Sneak 100", "advancements.adventure.blowback.description": "Kill a Breeze with a deflected Breeze-shot <PERSON> Charge", "advancements.adventure.blowback.title": "Blowback", "advancements.adventure.brush_armadillo.description": "Get Armadillo Scutes from an Armadillo using a Brush", "advancements.adventure.brush_armadillo.title": "Isn''t It Scute?", "advancements.adventure.bullseye.description": "Hit the bullseye of a Target block from at least 30 meters away", "advancements.adventure.bullseye.title": "Bullseye", "advancements.adventure.craft_decorated_pot_using_only_sherds.description": "Make a Decorated Pot out of 4 Pottery Sherds", "advancements.adventure.craft_decorated_pot_using_only_sherds.title": "Careful Restoration", "advancements.adventure.crafters_crafting_crafters.description": "Be near a Crafter when it crafts a Crafter", "advancements.adventure.crafters_crafting_crafters.title": "Crafters Crafting Crafters", "advancements.adventure.fall_from_world_height.description": "Free fall from the top of the world (build limit) to the bottom of the world and survive", "advancements.adventure.fall_from_world_height.title": "Caves & Cliffs", "advancements.adventure.hero_of_the_village.description": "Successfully defend a village from a raid", "advancements.adventure.hero_of_the_village.title": "Hero of the Village", "advancements.adventure.honey_block_slide.description": "Jump into a Honey Block to break your fall", "advancements.adventure.honey_block_slide.title": "Sticky Situation", "advancements.adventure.kill_a_mob.description": "Kill any hostile monster", "advancements.adventure.kill_a_mob.title": "Monster Hunter", "advancements.adventure.kill_all_mobs.description": "Kill one of every hostile monster", "advancements.adventure.kill_all_mobs.title": "Monsters Hunted", "advancements.adventure.kill_mob_near_sculk_catalyst.description": "Kill a mob near a Sculk Catalyst", "advancements.adventure.kill_mob_near_sculk_catalyst.title": "It Spreads", "advancements.adventure.lighten_up.description": "Scrape a Copper Bulb with an Axe to make it brighter", "advancements.adventure.lighten_up.title": "Lighten Up", "advancements.adventure.lightning_rod_with_villager_no_fire.description": "Protect a Villager from an undesired shock without starting a fire", "advancements.adventure.lightning_rod_with_villager_no_fire.title": "Surge Protector", "advancements.adventure.minecraft_trials_edition.description": "Step foot in a Trial Chamber", "advancements.adventure.minecraft_trials_edition.title": "Minecraft: Trial(s) Edition", "advancements.adventure.ol_betsy.description": "Shoot a Crossbow", "advancements.adventure.ol_betsy.title": "Ol'' <PERSON>", "advancements.adventure.overoverkill.description": "Deal 50 hearts of damage in a single hit using the Mace", "advancements.adventure.overoverkill.title": "Over-Overkill", "advancements.adventure.play_jukebox_in_meadows.description": "Make the Meadows come alive with the sound of music from a Jukebox", "advancements.adventure.play_jukebox_in_meadows.title": "Sound of Music", "advancements.adventure.read_power_from_chiseled_bookshelf.description": "Read the power signal of a Chiseled Bookshelf using a Comparator", "advancements.adventure.read_power_from_chiseled_bookshelf.title": "The Power of Books", "advancements.adventure.revaulting.description": "Unlock an Ominous Vault with an Ominous Trial Key", "advancements.adventure.revaulting.title": "Revaulting", "advancements.adventure.root.description": "Adventure, exploration and combat", "advancements.adventure.root.title": "Adventure", "advancements.adventure.salvage_sherd.description": "Brush a Suspicious block to obtain a Pottery Sherd", "advancements.adventure.salvage_sherd.title": "Respecting the Remnants", "advancements.adventure.shoot_arrow.description": "Shoot something with an Arrow", "advancements.adventure.shoot_arrow.title": "Take Aim", "advancements.adventure.sleep_in_bed.description": "Sleep in a Bed to change your respawn point", "advancements.adventure.sleep_in_bed.title": "Sweet Dreams", "advancements.adventure.sniper_duel.description": "Kill a Skeleton from at least 50 meters away", "advancements.adventure.sniper_duel.title": "<PERSON><PERSON><PERSON>", "advancements.adventure.spyglass_at_dragon.description": "Look at the Ender Dragon through a Spyglass", "advancements.adventure.spyglass_at_dragon.title": "Is It a Plane?", "advancements.adventure.spyglass_at_ghast.description": "Look at a Ghast through a Spyglass", "advancements.adventure.spyglass_at_ghast.title": "Is It a Balloon?", "advancements.adventure.spyglass_at_parrot.description": "Look at a Parrot through a Spyglass", "advancements.adventure.spyglass_at_parrot.title": "Is It a Bird?", "advancements.adventure.summon_iron_golem.description": "Summon an Iron Golem to help defend a village", "advancements.adventure.summon_iron_golem.title": "<PERSON><PERSON>", "advancements.adventure.throw_trident.description": "Throw a Trident at something.\nNote: Throwing away your only weapon is not a good idea.", "advancements.adventure.throw_trident.title": "A Throwaway Joke", "advancements.adventure.totem_of_undying.description": "Use a Totem of Undying to cheat death", "advancements.adventure.totem_of_undying.title": "Postmortal", "advancements.adventure.trade_at_world_height.description": "Trade with a Villager at the build height limit", "advancements.adventure.trade_at_world_height.title": "Star Trader", "advancements.adventure.trade.description": "Successfully trade with a Villager", "advancements.adventure.trade.title": "What a Deal!", "advancements.adventure.trim_with_all_exclusive_armor_patterns.description": "Apply these smithing templates at least once: <PERSON><PERSON>, <PERSON><PERSON>ut, R<PERSON>, Ward, Silence, Vex, Tide, Wayfinder", "advancements.adventure.trim_with_all_exclusive_armor_patterns.title": "Smithing with Style", "advancements.adventure.trim_with_any_armor_pattern.description": "Craft trimmed armor at a Smithing Table", "advancements.adventure.trim_with_any_armor_pattern.title": "Crafting a New Look", "advancements.adventure.two_birds_one_arrow.description": "Kill two Phantoms with a piercing Arrow", "advancements.adventure.two_birds_one_arrow.title": "Two Birds, One Arrow", "advancements.adventure.under_lock_and_key.description": "Unlock a Vault with a Trial Key", "advancements.adventure.under_lock_and_key.title": "Under Lock and Key", "advancements.adventure.very_very_frightening.description": "Strike a Villager with lightning", "advancements.adventure.very_very_frightening.title": "Very Very Frightening", "advancements.adventure.voluntary_exile.description": "Kill a raid captain.\nMaybe consider staying away from villages for the time being...", "advancements.adventure.voluntary_exile.title": "Voluntary Exile", "advancements.adventure.walk_on_powder_snow_with_leather_boots.description": "Walk on Powder Snow... without sinking in it", "advancements.adventure.walk_on_powder_snow_with_leather_boots.title": "Light as a Rabbit", "advancements.adventure.who_needs_rockets.description": "Use a Wind Charge to launch yourself upward 8 blocks", "advancements.adventure.who_needs_rockets.title": "Who Needs Rockets?", "advancements.adventure.whos_the_pillager_now.description": "Give a Pillager a taste of their own medicine", "advancements.adventure.whos_the_pillager_now.title": "Who''s the Pillager Now?", "advancements.empty": "There doesn''t seem to be anything here...", "advancements.end.dragon_breath.description": "Collect Dragon''s Breath in a Glass Bottle", "advancements.end.dragon_breath.title": "You Need a Mint", "advancements.end.dragon_egg.description": "Hold the Dragon Egg", "advancements.end.dragon_egg.title": "The Next Generation", "advancements.end.elytra.description": "Find Elytra", "advancements.end.elytra.title": "Sky''s the Limit", "advancements.end.enter_end_gateway.description": "Escape the island", "advancements.end.enter_end_gateway.title": "Remote Getaway", "advancements.end.find_end_city.description": "Go on in, what could happen?", "advancements.end.find_end_city.title": "The City at the End of the Game", "advancements.end.kill_dragon.description": "Good luck", "advancements.end.kill_dragon.title": "Free the End", "advancements.end.levitate.description": "Levitate up 50 blocks from the attacks of a Shulker", "advancements.end.levitate.title": "Great View From Up Here", "advancements.end.respawn_dragon.description": "Respawn the Ender Dragon", "advancements.end.respawn_dragon.title": "The End... Again...", "advancements.end.root.description": "Or the beginning?", "advancements.end.root.title": "The End", "advancements.husbandry.allay_deliver_cake_to_note_block.description": "Have an Allay drop a Cake at a Note Block", "advancements.husbandry.allay_deliver_cake_to_note_block.title": "Birthday Song", "advancements.husbandry.allay_deliver_item_to_player.description": "Have an Allay deliver items to you", "advancements.husbandry.allay_deliver_item_to_player.title": "You''ve Got a Friend in Me", "advancements.husbandry.axolotl_in_a_bucket.description": "Catch an Axolotl in a Bucket", "advancements.husbandry.axolotl_in_a_bucket.title": "The Cutest Predator", "advancements.husbandry.balanced_diet.description": "Eat everything that is edible, even if it''s not good for you", "advancements.husbandry.balanced_diet.title": "A Balanced Diet", "advancements.husbandry.breed_all_animals.description": "Breed all the animals!", "advancements.husbandry.breed_all_animals.title": "Two by Two", "advancements.husbandry.breed_an_animal.description": "Breed two animals together", "advancements.husbandry.breed_an_animal.title": "The Parrots and the Bats", "advancements.husbandry.complete_catalogue.description": "Tame all Cat variants!", "advancements.husbandry.complete_catalogue.title": "A Complete Catalogue", "advancements.husbandry.feed_snifflet.description": "Feed a Snifflet", "advancements.husbandry.feed_snifflet.title": "Little Sniffs", "advancements.husbandry.fishy_business.description": "Catch a fish", "advancements.husbandry.fishy_business.title": "Fishy Business", "advancements.husbandry.froglights.description": "Have all Froglights in your inventory", "advancements.husbandry.froglights.title": "With Our Powers Combined!", "advancements.husbandry.kill_axolotl_target.description": "Team up with an Axolotl and win a fight", "advancements.husbandry.kill_axolotl_target.title": "The Healing Power of Friendship!", "advancements.husbandry.leash_all_frog_variants.description": "Get each Frog variant on a Lead", "advancements.husbandry.leash_all_frog_variants.title": "When the Squad Hops into Town", "advancements.husbandry.make_a_sign_glow.description": "Make the text of any kind of sign glow", "advancements.husbandry.make_a_sign_glow.title": "Glow and Behold!", "advancements.husbandry.netherite_hoe.description": "Use a Netherite Ingot to upgrade a Hoe, and then reevaluate your life choices", "advancements.husbandry.netherite_hoe.title": "Serious Dedication", "advancements.husbandry.obtain_sniffer_egg.description": "Obtain a Sniffer Egg", "advancements.husbandry.obtain_sniffer_egg.title": "Smells Interesting", "advancements.husbandry.plant_any_sniffer_seed.description": "Plant any Sniffer seed", "advancements.husbandry.plant_any_sniffer_seed.title": "Planting the Past", "advancements.husbandry.plant_seed.description": "Plant a seed and watch it grow", "advancements.husbandry.plant_seed.title": "A Seedy Place", "advancements.husbandry.remove_wolf_armor.description": "Remove Wolf Armor from a Wolf using Shears", "advancements.husbandry.remove_wolf_armor.title": "Shear Brilliance", "advancements.husbandry.repair_wolf_armor.description": "Fully repair damaged Wolf Armor using Armadillo Scutes", "advancements.husbandry.repair_wolf_armor.title": "Good as New", "advancements.husbandry.ride_a_boat_with_a_goat.description": "Get in a Boat and float with a Goat", "advancements.husbandry.ride_a_boat_with_a_goat.title": "Whatever Floats Your Goat!", "advancements.husbandry.root.description": "The world is full of friends and food", "advancements.husbandry.root.title": "Husbandry", "advancements.husbandry.safely_harvest_honey.description": "Use a Campfire to collect Honey from a Beehive using a Glass Bottle without aggravating the Bees", "advancements.husbandry.safely_harvest_honey.title": "Bee Our Guest", "advancements.husbandry.silk_touch_nest.description": "Move a Bee Nest or Beehive, with 3 Bees inside, using Silk Touch", "advancements.husbandry.silk_touch_nest.title": "Total Beelocation", "advancements.husbandry.tactical_fishing.description": "Catch a Fish... without a Fishing Rod!", "advancements.husbandry.tactical_fishing.title": "Tactical Fishing", "advancements.husbandry.tadpole_in_a_bucket.description": "Catch a Tadpole in a Bucket", "advancements.husbandry.tadpole_in_a_bucket.title": "Bukkit Bukkit", "advancements.husbandry.tame_an_animal.description": "Tame an animal", "advancements.husbandry.tame_an_animal.title": "Best Friends Forever", "advancements.husbandry.wax_off.description": "Scrape Wax off of a Copper block!", "advancements.husbandry.wax_off.title": "Wax Off", "advancements.husbandry.wax_on.description": "Apply Honeycomb to a Copper block!", "advancements.husbandry.wax_on.title": "Wax On", "advancements.husbandry.whole_pack.description": "Tame one of each Wolf variant", "advancements.husbandry.whole_pack.title": "The Whole Pack", "advancements.nether.all_effects.description": "Have every effect applied at the same time", "advancements.nether.all_effects.title": "How Did We Get Here?", "advancements.nether.all_potions.description": "Have every potion effect applied at the same time", "advancements.nether.all_potions.title": "A Furious Cocktail", "advancements.nether.brew_potion.description": "Brew a Potion", "advancements.nether.brew_potion.title": "Local Brewery", "advancements.nether.charge_respawn_anchor.description": "Charge a Respawn Anchor to the maximum", "advancements.nether.charge_respawn_anchor.title": "Not Quite \"Nine\" Lives", "advancements.nether.create_beacon.description": "Construct and place a Beacon", "advancements.nether.create_beacon.title": "Bring Home the Beacon", "advancements.nether.create_full_beacon.description": "Bring a Beacon to full power", "advancements.nether.create_full_beacon.title": "Beaconator", "advancements.nether.distract_piglin.description": "Distract <PERSON>lins with gold", "advancements.nether.distract_piglin.title": "Oh Shiny", "advancements.nether.explore_nether.description": "Explore all Nether biomes", "advancements.nether.explore_nether.title": "Hot Tourist Destinations", "advancements.nether.fast_travel.description": "Use the Nether to travel 7 km in the Overworld", "advancements.nether.fast_travel.title": "Subspace Bubble", "advancements.nether.find_bastion.description": "Enter a Bastion Remnant", "advancements.nether.find_bastion.title": "Those Were the Days", "advancements.nether.find_fortress.description": "Break your way into a Nether Fortress", "advancements.nether.find_fortress.title": "A Terrible Fortress", "advancements.nether.get_wither_skull.description": "Obtain a Wither Skeleton''s skull", "advancements.nether.get_wither_skull.title": "Spooky Scary Skeleton", "advancements.nether.loot_bastion.description": "Loot a Chest in a Bastion Remnant", "advancements.nether.loot_bastion.title": "War Pigs", "advancements.nether.netherite_armor.description": "Get a full suit of Netherite armor", "advancements.nether.netherite_armor.title": "Cover Me in Debris", "advancements.nether.obtain_ancient_debris.description": "Obtain Ancient Debris", "advancements.nether.obtain_ancient_debris.title": "Hidden in the Depths", "advancements.nether.obtain_blaze_rod.description": "Relieve a Blaze of its rod", "advancements.nether.obtain_blaze_rod.title": "Into Fire", "advancements.nether.obtain_crying_obsidian.description": "Obtain Crying Obsidian", "advancements.nether.obtain_crying_obsidian.title": "Who is Cutting Onions?", "advancements.nether.return_to_sender.description": "<PERSON><PERSON>y a <PERSON><PERSON><PERSON> with a fireball", "advancements.nether.return_to_sender.title": "Return to Sender", "advancements.nether.ride_strider_in_overworld_lava.description": "Take a Strider for a loooong ride on a lava lake in the Overworld", "advancements.nether.ride_strider_in_overworld_lava.title": "Feels Like Home", "advancements.nether.ride_strider.description": "Ride a Strider with a Warped Fungus on a Stick", "advancements.nether.ride_strider.title": "This Boat Has Legs", "advancements.nether.root.description": "Bring summer clothes", "advancements.nether.root.title": "Nether", "advancements.nether.summon_wither.description": "<PERSON><PERSON><PERSON> the Wither", "advancements.nether.summon_wither.title": "Withering Heights", "advancements.nether.uneasy_alliance.description": "Rescue a Ghast from the Nether, bring it safely home to the Overworld... and then kill it", "advancements.nether.uneasy_alliance.title": "Uneasy Alliance", "advancements.nether.use_lodestone.description": "Use a Compass on a Lodestone", "advancements.nether.use_lodestone.title": "Country Lode, Take Me Home", "advancements.progress": "{0}/{1}", "advancements.sad_label": ":(", "advancements.story.cure_zombie_villager.description": "Weaken and then cure a Zombie Villager", "advancements.story.cure_zombie_villager.title": "Zombie Doctor", "advancements.story.deflect_arrow.description": "Deflect a projectile with a Shield", "advancements.story.deflect_arrow.title": "Not Today, Thank You", "advancements.story.enchant_item.description": "Enchant an item at an Enchanting Table", "advancements.story.enchant_item.title": "Enchanter", "advancements.story.enter_the_end.description": "Enter the End Portal", "advancements.story.enter_the_end.title": "The End?", "advancements.story.enter_the_nether.description": "Build, light and enter a Nether Portal", "advancements.story.enter_the_nether.title": "We Need to Go Deeper", "advancements.story.follow_ender_eye.description": "Follow an Eye of Ender", "advancements.story.follow_ender_eye.title": "Eye Spy", "advancements.story.form_obsidian.description": "Obtain a block of Obsidian", "advancements.story.form_obsidian.title": "Ice Bucket Challenge", "advancements.story.iron_tools.description": "Upgrade your Pickaxe", "advancements.story.iron_tools.title": "Isn''t It Iron Pick", "advancements.story.lava_bucket.description": "Fill a Bucket with lava", "advancements.story.lava_bucket.title": "Hot Stuff", "advancements.story.mine_diamond.description": "Acquire diamonds", "advancements.story.mine_diamond.title": "Diamonds!", "advancements.story.mine_stone.description": "Mine Stone with your new Pickaxe", "advancements.story.mine_stone.title": "Stone Age", "advancements.story.obtain_armor.description": "Protect yourself with a piece of iron armor", "advancements.story.obtain_armor.title": "Suit Up", "advancements.story.root.description": "The heart and story of the game", "advancements.story.root.title": "Minecraft", "advancements.story.shiny_gear.description": "Diamond armor saves lives", "advancements.story.shiny_gear.title": "Cover Me with Diamonds", "advancements.story.smelt_iron.description": "Smelt an Iron Ingot", "advancements.story.smelt_iron.title": "Acquire Hardware", "advancements.story.upgrade_tools.description": "Construct a better Pickaxe", "advancements.story.upgrade_tools.title": "Getting an Upgrade", "advancements.toast.challenge": "Challenge Complete!", "advancements.toast.goal": "Goal Reached!", "advancements.toast.task": "Advancement Made!", "argument.anchor.invalid": "Invalid entity anchor position {0}", "argument.angle.incomplete": "Incomplete (expected 1 angle)", "argument.angle.invalid": "Invalid angle", "argument.block.id.invalid": "Unknown block type ''{0}''", "argument.block.property.duplicate": "Property ''{0}'' can only be set once for block {1}", "argument.block.property.invalid": "Block {0} does not accept ''{1}'' for {2} property", "argument.block.property.novalue": "Expected value for property ''{0}'' on block {1}", "argument.block.property.unclosed": "Expected closing ] for block state properties", "argument.block.property.unknown": "Block {0} does not have property ''{1}''", "argument.block.tag.disallowed": "Tags aren''t allowed here, only actual blocks", "argument.color.invalid": "Unknown color ''{0}''", "argument.component.invalid": "Invalid chat component: {0}", "argument.criteria.invalid": "Unknown criterion ''{0}''", "argument.dimension.invalid": "Unknown dimension ''{0}''", "argument.double.big": "Double must not be more than {0}, found {1}", "argument.double.low": "Double must not be less than {0}, found {1}", "argument.entity.invalid": "Invalid name or UUID", "argument.entity.notfound.entity": "No entity was found", "argument.entity.notfound.player": "No player was found", "argument.entity.options.advancements.description": "Players with advancements", "argument.entity.options.distance.description": "Distance to entity", "argument.entity.options.distance.negative": "Distance cannot be negative", "argument.entity.options.dx.description": "Entities between x and x + dx", "argument.entity.options.dy.description": "Entities between y and y + dy", "argument.entity.options.dz.description": "Entities between z and z + dz", "argument.entity.options.gamemode.description": "Players with game mode", "argument.entity.options.inapplicable": "Option ''{0}'' isn''t applicable here", "argument.entity.options.level.description": "Experience level", "argument.entity.options.level.negative": "Level shouldn''t be negative", "argument.entity.options.limit.description": "Maximum number of entities to return", "argument.entity.options.limit.toosmall": "Limit must be at least 1", "argument.entity.options.mode.invalid": "Invalid or unknown game mode ''{0}''", "argument.entity.options.name.description": "Entity name", "argument.entity.options.nbt.description": "Entities with NBT", "argument.entity.options.predicate.description": "Custom predicate", "argument.entity.options.scores.description": "Entities with scores", "argument.entity.options.sort.description": "Sort the entities", "argument.entity.options.sort.irreversible": "Invalid or unknown sort type ''{0}''", "argument.entity.options.tag.description": "Entities with tag", "argument.entity.options.team.description": "Entities on team", "argument.entity.options.type.description": "Entities of type", "argument.entity.options.type.invalid": "Invalid or unknown entity type ''{0}''", "argument.entity.options.unknown": "Unknown option ''{0}''", "argument.entity.options.unterminated": "Expected end of options", "argument.entity.options.valueless": "Expected value for option ''{0}''", "argument.entity.options.x_rotation.description": "Entity''s x rotation", "argument.entity.options.x.description": "x position", "argument.entity.options.y_rotation.description": "Entity''s y rotation", "argument.entity.options.y.description": "y position", "argument.entity.options.z.description": "z position", "argument.entity.selector.allEntities": "All entities", "argument.entity.selector.allPlayers": "All players", "argument.entity.selector.missing": "Missing selector type", "argument.entity.selector.nearestEntity": "Nearest entity", "argument.entity.selector.nearestPlayer": "Nearest player", "argument.entity.selector.not_allowed": "Selector not allowed", "argument.entity.selector.randomPlayer": "Random player", "argument.entity.selector.self": "Current entity", "argument.entity.selector.unknown": "Unknown selector type ''{0}''", "argument.entity.toomany": "Only one entity is allowed, but the provided selector allows more than one", "argument.enum.invalid": "Invalid value \"{0}\"", "argument.float.big": "Float must not be more than {0}, found {1}", "argument.float.low": "Float must not be less than {0}, found {1}", "argument.gamemode.invalid": "Unknown game mode: {0}", "argument.id.invalid": "Invalid ID", "argument.id.unknown": "Unknown ID: {0}", "argument.integer.big": "Integer must not be more than {0}, found {1}", "argument.integer.low": "Integer must not be less than {0}, found {1}", "argument.item.id.invalid": "Unknown item ''{0}''", "argument.item.tag.disallowed": "Tags aren''t allowed here, only actual items", "argument.literal.incorrect": "Expected literal {0}", "argument.long.big": "Long must not be more than {0}, found {1}", "argument.long.low": "Long must not be less than {0}, found {1}", "argument.message.too_long": "Chat message was too long ({0} > maximum {1} characters)", "argument.nbt.array.invalid": "Invalid array type ''{0}''", "argument.nbt.array.mixed": "Can''t insert {0} into {1}", "argument.nbt.expected.key": "Expected key", "argument.nbt.expected.value": "Expected value", "argument.nbt.list.mixed": "Can''t insert {0} into list of {1}", "argument.nbt.trailing": "Unexpected trailing data", "argument.player.entities": "Only players may be affected by this command, but the provided selector includes entities", "argument.player.toomany": "Only one player is allowed, but the provided selector allows more than one", "argument.player.unknown": "That player does not exist", "argument.pos.missing.double": "Expected a coordinate", "argument.pos.missing.int": "Expected a block position", "argument.pos.mixed": "Cannot mix world & local coordinates (everything must either use ^ or not)", "argument.pos.outofbounds": "That position is outside the allowed boundaries.", "argument.pos.outofworld": "That position is out of this world!", "argument.pos.unloaded": "That position is not loaded", "argument.pos2d.incomplete": "Incomplete (expected 2 coordinates)", "argument.pos3d.incomplete": "Incomplete (expected 3 coordinates)", "argument.range.empty": "Expected value or range of values", "argument.range.ints": "Only whole numbers allowed, not decimals", "argument.range.swapped": "Min cannot be bigger than max", "argument.resource_or_id.failed_to_parse": "Failed to parse structure: {0}", "argument.resource_or_id.invalid": "Invalid id or tag", "argument.resource_tag.invalid_type": "Tag ''{0}'' has wrong type ''{1}'' (expected ''{2}'')", "argument.resource_tag.not_found": "Can''t find tag ''{0}'' of type ''{1}''", "argument.resource.invalid_type": "Element ''{0}'' has wrong type ''{1}'' (expected ''{2}'')", "argument.resource.not_found": "Can''t find element ''{0}'' of type ''{1}''", "argument.rotation.incomplete": "Incomplete (expected 2 coordinates)", "argument.scoreboardDisplaySlot.invalid": "Unknown display slot ''{0}''", "argument.scoreHolder.empty": "No relevant score holders could be found", "argument.style.invalid": "Invalid style: {0}", "argument.time.invalid_tick_count": "The tick count must be non-negative", "argument.time.invalid_unit": "Invalid unit", "argument.time.tick_count_too_low": "The tick count must not be less than {0}, found {1}", "argument.uuid.invalid": "Invalid UUID", "block.minecraft.set_spawn": "Respawn point set", "build.tooHigh": "Height limit for building is {0}", "chat.cannotSend": "Cannot send chat message", "chat.coordinates": "{0}, {1}, {2}", "chat.coordinates.tooltip": "Click to teleport", "chat.copy": "Copy to Clipboard", "chat.copy.click": "Click to <PERSON><PERSON> to Clipboard", "chat.deleted_marker": "This chat message has been deleted by the server.", "chat.disabled.chain_broken": "<PERSON><PERSON> disabled due to broken chain. Please try reconnecting.", "chat.disabled.expiredProfileKey": "<PERSON><PERSON> disabled due to expired profile public key. Please try reconnecting.", "chat.disabled.invalid_command_signature": "Command had unexpected or missing command argument signatures.", "chat.disabled.invalid_signature": "<PERSON><PERSON> had an invalid signature. Please try reconnecting.", "chat.disabled.launcher": "Chat disabled by launcher option. Cannot send message.", "chat.disabled.missingProfileKey": "<PERSON><PERSON> disabled due to missing profile public key. Please try reconnecting.", "chat.disabled.options": "Chat disabled in client options.", "chat.disabled.out_of_order_chat": "<PERSON><PERSON> received out-of-order. Did your system time change?", "chat.disabled.profile": "Chat is not allowed by account settings. Press ''{0}'' again for more information.", "chat.disabled.profile.moreInfo": "Chat is not allowed by account settings. Cannot send or view messages.", "chat.editBox": "chat", "chat.filtered": "Filtered by the server.", "chat.filtered_full": "The server has hidden your message for some players.", "chat.link.confirm": "Are you sure you want to open the following website?", "chat.link.confirmTrusted": "Do you want to open this link or copy it to your clipboard?", "chat.link.open": "Open in Browser", "chat.link.warning": "Never open links from people that you don''t trust!", "chat.queue": "[+{0} pending line(s)]", "chat.square_brackets": "[{0}]", "chat.tag.error": "Server sent invalid message.", "chat.tag.modified": "Message modified by the server. Original:", "chat.tag.not_secure": "Unverified message. Cannot be reported.", "chat.tag.system": "Server message. Cannot be reported.", "chat.tag.system_single_player": "Server message.", "chat.type.admin": "[{0}: {1}]", "chat.type.advancement.challenge": "{0} has completed the challenge {1}", "chat.type.advancement.goal": "{0} has reached the goal {1}", "chat.type.advancement.task": "{0} has made the advancement {1}", "chat.type.announcement": "[{0}] {1}", "chat.type.emote": "* {0} {1}", "chat.type.team.hover": "Message Team", "chat.type.team.sent": "-> {0} <{1}> {2}", "chat.type.team.text": "{0} <{1}> {2}", "chat.type.text": "<{0}> {1}", "chat.type.text.narrate": "{0} says {1}", "chat.validation_error": "Chat validation error", "clear.failed.multiple": "No items were found on {0} players", "clear.failed.single": "No items were found on player {0}", "command.context.here": "<--[HERE]", "command.context.parse_error": "{0} at position {1}: {2}", "command.exception": "Could not parse command: {0}", "command.expected.separator": "Expected whitespace to end one argument, but found trailing data", "command.failed": "An unexpected error occurred trying to execute that command", "command.forkLimit": "Maximum number of contexts ({0}) reached", "command.unknown.argument": "Incorrect argument for command", "command.unknown.command": "Unknown or incomplete command, see below for error", "commands.advancement.criterionNotFound": "The advancement {0} does not contain the criterion ''{1}''", "commands.advancement.grant.criterion.to.many.failure": "Couldn''t grant criterion ''{0}'' of advancement {1} to {2} players as they already have it", "commands.advancement.grant.criterion.to.many.success": "Granted criterion ''{0}'' of advancement {1} to {2} players", "commands.advancement.grant.criterion.to.one.failure": "Couldn''t grant criterion ''{0}'' of advancement {1} to {2} as they already have it", "commands.advancement.grant.criterion.to.one.success": "Granted criterion ''{0}'' of advancement {1} to {2}", "commands.advancement.grant.many.to.many.failure": "Couldn''t grant {0} advancements to {1} players as they already have them", "commands.advancement.grant.many.to.many.success": "Granted {0} advancements to {1} players", "commands.advancement.grant.many.to.one.failure": "Couldn''t grant {0} advancements to {1} as they already have them", "commands.advancement.grant.many.to.one.success": "Granted {0} advancements to {1}", "commands.advancement.grant.one.to.many.failure": "Couldn''t grant advancement {0} to {1} players as they already have it", "commands.advancement.grant.one.to.many.success": "Granted the advancement {0} to {1} players", "commands.advancement.grant.one.to.one.failure": "Couldn''t grant advancement {0} to {1} as they already have it", "commands.advancement.grant.one.to.one.success": "Granted the advancement {0} to {1}", "commands.advancement.revoke.criterion.to.many.failure": "Couldn''t revoke criterion ''{0}'' of advancement {1} from {2} players as they don''t have it", "commands.advancement.revoke.criterion.to.many.success": "Revoked criterion ''{0}'' of advancement {1} from {2} players", "commands.advancement.revoke.criterion.to.one.failure": "Couldn''t revoke criterion ''{0}'' of advancement {1} from {2} as they don''t have it", "commands.advancement.revoke.criterion.to.one.success": "Revoked criterion ''{0}'' of advancement {1} from {2}", "commands.advancement.revoke.many.to.many.failure": "Couldn''t revoke {0} advancements from {1} players as they don''t have them", "commands.advancement.revoke.many.to.many.success": "Revoked {0} advancements from {1} players", "commands.advancement.revoke.many.to.one.failure": "Couldn''t revoke {0} advancements from {1} as they don''t have them", "commands.advancement.revoke.many.to.one.success": "Revoked {0} advancements from {1}", "commands.advancement.revoke.one.to.many.failure": "Couldn''t revoke advancement {0} from {1} players as they don''t have it", "commands.advancement.revoke.one.to.many.success": "Revoked the advancement {0} from {1} players", "commands.advancement.revoke.one.to.one.failure": "Couldn''t revoke advancement {0} from {1} as they don''t have it", "commands.advancement.revoke.one.to.one.success": "Revoked the advancement {0} from {1}", "commands.attribute.base_value.get.success": "Base value of attribute {0} for entity {1} is {2}", "commands.attribute.base_value.reset.success": "Base value for attribute {0} for entity {1} reset to default {2}", "commands.attribute.base_value.set.success": "Base value for attribute {0} for entity {1} set to {2}", "commands.attribute.failed.entity": "{0} is not a valid entity for this command", "commands.attribute.failed.modifier_already_present": "Modifier {0} is already present on attribute {1} for entity {2}", "commands.attribute.failed.no_attribute": "Entity {0} has no attribute {1}", "commands.attribute.failed.no_modifier": "Attribute {0} for entity {1} has no modifier {2}", "commands.attribute.modifier.add.success": "Added modifier {0} to attribute {1} for entity {2}", "commands.attribute.modifier.remove.success": "Removed modifier {0} from attribute {1} for entity {2}", "commands.attribute.modifier.value.get.success": "Value of modifier {0} on attribute {1} for entity {2} is {3}", "commands.attribute.value.get.success": "Value of attribute {0} for entity {1} is {2}", "commands.ban.failed": "Nothing changed. The player is already banned", "commands.ban.success": "Banned {0}: {1}", "commands.banip.failed": "Nothing changed. That IP is already banned", "commands.banip.info": "This ban affects {0} player(s): {1}", "commands.banip.invalid": "Invalid IP address or unknown player", "commands.banip.success": "Banned IP {0}: {1}", "commands.banlist.entry": "{0} was banned by {1}: {2}", "commands.banlist.entry.unknown": "(Unknown)", "commands.banlist.list": "There are {0} ban(s):", "commands.banlist.none": "There are no bans", "commands.bossbar.create.failed": "A bossbar already exists with the ID ''{0}''", "commands.bossbar.create.success": "Created custom bossbar {0}", "commands.bossbar.get.max": "Custom bossbar {0} has a maximum of {1}", "commands.bossbar.get.players.none": "Custom bossbar {0} has no players currently online", "commands.bossbar.get.players.some": "Custom bossbar {0} has {1} player(s) currently online: {2}", "commands.bossbar.get.value": "Custom bossbar {0} has a value of {1}", "commands.bossbar.get.visible.hidden": "Custom bossbar {0} is currently hidden", "commands.bossbar.get.visible.visible": "Custom bossbar {0} is currently shown", "commands.bossbar.list.bars.none": "There are no custom bossbars active", "commands.bossbar.list.bars.some": "There are {0} custom bossbar(s) active: {1}", "commands.bossbar.remove.success": "Removed custom bossbar {0}", "commands.bossbar.set.color.success": "Custom bossbar {0} has changed color", "commands.bossbar.set.color.unchanged": "Nothing changed. That''s already the color of this bossbar", "commands.bossbar.set.max.success": "Custom bossbar {0} has changed maximum to {1}", "commands.bossbar.set.max.unchanged": "Nothing changed. That''s already the max of this bossbar", "commands.bossbar.set.name.success": "Custom bossbar {0} has been renamed", "commands.bossbar.set.name.unchanged": "Nothing changed. That''s already the name of this bossbar", "commands.bossbar.set.players.success.none": "Custom bossbar {0} no longer has any players", "commands.bossbar.set.players.success.some": "Custom bossbar {0} now has {1} player(s): {2}", "commands.bossbar.set.players.unchanged": "Nothing changed. Those players are already on the bossbar with nobody to add or remove", "commands.bossbar.set.style.success": "Custom bossbar {0} has changed style", "commands.bossbar.set.style.unchanged": "Nothing changed. That''s already the style of this bossbar", "commands.bossbar.set.value.success": "Custom bossbar {0} has changed value to {1}", "commands.bossbar.set.value.unchanged": "Nothing changed. That''s already the value of this bossbar", "commands.bossbar.set.visibility.unchanged.hidden": "Nothing changed. The bossbar is already hidden", "commands.bossbar.set.visibility.unchanged.visible": "Nothing changed. The bossbar is already visible", "commands.bossbar.set.visible.success.hidden": "Custom bossbar {0} is now hidden", "commands.bossbar.set.visible.success.visible": "Custom bossbar {0} is now visible", "commands.bossbar.unknown": "No bossbar exists with the ID ''{0}''", "commands.clear.success.multiple": "Removed {0} item(s) from {1} players", "commands.clear.success.single": "Removed {0} item(s) from player {1}", "commands.clear.test.multiple": "Found {0} matching item(s) on {1} players", "commands.clear.test.single": "Found {0} matching item(s) on player {1}", "commands.clone.failed": "No blocks were cloned", "commands.clone.overlap": "The source and destination areas cannot overlap", "commands.clone.success": "Successfully cloned {0} block(s)", "commands.clone.toobig": "Too many blocks in the specified area (maximum {0}, specified {1})", "commands.damage.invulnerable": "Target is invulnerable to the given damage type", "commands.damage.success": "Applied {0} damage to {1}", "commands.data.block.get": "{0} on block {1}, {2}, {3} after scale factor of {4} is {5}", "commands.data.block.invalid": "The target block is not a block entity", "commands.data.block.modified": "Modified block data of {0}, {1}, {2}", "commands.data.block.query": "{0}, {1}, {2} has the following block data: {3}", "commands.data.entity.get": "{0} on {1} after scale factor of {2} is {3}", "commands.data.entity.invalid": "Unable to modify player data", "commands.data.entity.modified": "Modified entity data of {0}", "commands.data.entity.query": "{0} has the following entity data: {1}", "commands.data.get.invalid": "Can''t get {0}; only numeric tags are allowed", "commands.data.get.multiple": "This argument accepts a single NBT value", "commands.data.get.unknown": "Can''t get {0}; tag doesn''t exist", "commands.data.merge.failed": "Nothing changed. The specified properties already have these values", "commands.data.modify.expected_list": "Expected list, got: {0}", "commands.data.modify.expected_object": "Expected object, got: {0}", "commands.data.modify.expected_value": "Expected value, got: {0}", "commands.data.modify.invalid_index": "Invalid list index: {0}", "commands.data.modify.invalid_substring": "Invalid substring indices: {0} to {1}", "commands.data.storage.get": "{0} in storage {1} after scale factor of {2} is {3}", "commands.data.storage.modified": "Modified storage {0}", "commands.data.storage.query": "Storage {0} has the following contents: {1}", "commands.datapack.disable.failed": "Pack ''{0}'' is not enabled!", "commands.datapack.disable.failed.feature": "Pack ''{0}'' cannot be disabled, since it is part of an enabled flag!", "commands.datapack.enable.failed": "Pack ''{0}'' is already enabled!", "commands.datapack.enable.failed.no_flags": "Pack ''{0}'' cannot be enabled, since required flags are not enabled in this world: {1}!", "commands.datapack.list.available.none": "There are no more data packs available", "commands.datapack.list.available.success": "There are {0} data pack(s) available: {1}", "commands.datapack.list.enabled.none": "There are no data packs enabled", "commands.datapack.list.enabled.success": "There are {0} data pack(s) enabled: {1}", "commands.datapack.modify.disable": "Disabling data pack {0}", "commands.datapack.modify.enable": "Enabling data pack {0}", "commands.datapack.unknown": "Unknown data pack ''{0}''", "commands.debug.alreadyRunning": "The tick profiler is already started", "commands.debug.function.noRecursion": "Can''t trace from inside of function", "commands.debug.function.noReturnRun": "Tracing can''t be used with return run", "commands.debug.function.success.multiple": "Traced {0} command(s) from {1} functions to output file {2}", "commands.debug.function.success.single": "Traced {0} command(s) from function ''{1}'' to output file {2}", "commands.debug.function.traceFailed": "Failed to trace function", "commands.debug.notRunning": "The tick profiler hasn''t started", "commands.debug.started": "Started tick profiling", "commands.debug.stopped": "Stopped tick profiling after {0} second(s) and {1} tick(s) ({2} tick(s) per second)", "commands.defaultgamemode.success": "The default game mode is now {0}", "commands.deop.failed": "Nothing changed. The player is not an operator", "commands.deop.success": "Made {0} no longer a server operator", "commands.difficulty.failure": "The difficulty did not change; it is already set to {0}", "commands.difficulty.query": "The difficulty is {0}", "commands.difficulty.success": "The difficulty has been set to {0}", "commands.drop.no_held_items": "Entity can''t hold any items", "commands.drop.no_loot_table": "Entity {0} has no loot table", "commands.drop.no_loot_table.block": "Block {0} has no loot table", "commands.drop.success.multiple": "Dropped {0} items", "commands.drop.success.multiple_with_table": "Dropped {0} items from loot table {1}", "commands.drop.success.single": "Dropped {0} {1}", "commands.drop.success.single_with_table": "Dropped {0} {1} from loot table {2}", "commands.effect.clear.everything.failed": "Target has no effects to remove", "commands.effect.clear.everything.success.multiple": "Removed every effect from {0} targets", "commands.effect.clear.everything.success.single": "Removed every effect from {0}", "commands.effect.clear.specific.failed": "Target doesn''t have the requested effect", "commands.effect.clear.specific.success.multiple": "Removed effect {0} from {1} targets", "commands.effect.clear.specific.success.single": "Removed effect {0} from {1}", "commands.effect.give.failed": "Unable to apply this effect (target is either immune to effects, or has something stronger)", "commands.effect.give.success.multiple": "Applied effect {0} to {1} targets", "commands.effect.give.success.single": "Applied effect {0} to {1}", "commands.enchant.failed": "Nothing changed. Targets either have no item in their hands or the enchantment could not be applied", "commands.enchant.failed.entity": "{0} is not a valid entity for this command", "commands.enchant.failed.incompatible": "{0} cannot support that enchantment", "commands.enchant.failed.itemless": "{0} is not holding any item", "commands.enchant.failed.level": "{0} is higher than the maximum level of {1} supported by that enchantment", "commands.enchant.success.multiple": "Applied enchantment {0} to {1} entities", "commands.enchant.success.single": "Applied enchantment {0} to {1}''s item", "commands.execute.blocks.toobig": "Too many blocks in the specified area (maximum {0}, specified {1})", "commands.execute.conditional.fail": "Test failed", "commands.execute.conditional.fail_count": "Test failed, count: {0}", "commands.execute.conditional.pass": "Test passed", "commands.execute.conditional.pass_count": "Test passed, count: {0}", "commands.execute.function.instantiationFailure": "Failed to instantiate function {0}: {1}", "commands.experience.add.levels.success.multiple": "Gave {0} experience levels to {1} players", "commands.experience.add.levels.success.single": "Gave {0} experience levels to {1}", "commands.experience.add.points.success.multiple": "Gave {0} experience points to {1} players", "commands.experience.add.points.success.single": "Gave {0} experience points to {1}", "commands.experience.query.levels": "{0} has {1} experience levels", "commands.experience.query.points": "{0} has {1} experience points", "commands.experience.set.levels.success.multiple": "Set {0} experience levels on {1} players", "commands.experience.set.levels.success.single": "Set {0} experience levels on {1}", "commands.experience.set.points.invalid": "<PERSON><PERSON> set experience points above the maximum points for the player''s current level", "commands.experience.set.points.success.multiple": "Set {0} experience points on {1} players", "commands.experience.set.points.success.single": "Set {0} experience points on {1}", "commands.fill.failed": "No blocks were filled", "commands.fill.success": "Successfully filled {0} block(s)", "commands.fill.toobig": "Too many blocks in the specified area (maximum {0}, specified {1})", "commands.fillbiome.success": "Biomes set between {0}, {1}, {2} and {3}, {4}, {5}", "commands.fillbiome.success.count": "{0} biome entry/entries set between {1}, {2}, {3} and {4}, {5}, {6}", "commands.fillbiome.toobig": "Too many blocks in the specified volume (maximum {0}, specified {1})", "commands.forceload.added.failure": "No chunks were marked for force loading", "commands.forceload.added.multiple": "Marked {0} chunks in {1} from {2} to {3} to be force loaded", "commands.forceload.added.none": "No force loaded chunks were found in {0}", "commands.forceload.added.single": "Marked chunk {0} in {1} to be force loaded", "commands.forceload.list.multiple": "{0} force loaded chunks were found in {1} at: {2}", "commands.forceload.list.single": "A force loaded chunk was found in {0} at: {1}", "commands.forceload.query.failure": "Chunk at {0} in {1} is not marked for force loading", "commands.forceload.query.success": "Chunk at {0} in {1} is marked for force loading", "commands.forceload.removed.all": "Unmarked all force loaded chunks in {0}", "commands.forceload.removed.failure": "No chunks were removed from force loading", "commands.forceload.removed.multiple": "Unmarked {0} chunks in {1} from {2} to {3} for force loading", "commands.forceload.removed.single": "Unmarked chunk {0} in {1} for force loading", "commands.forceload.toobig": "Too many chunks in the specified area (maximum {0}, specified {1})", "commands.function.error.argument_not_compound": "Invalid argument type: {0}, expected Compound", "commands.function.error.missing_argument": "Missing argument {1} to function {0}", "commands.function.error.missing_arguments": "Missing arguments to function {0}", "commands.function.error.parse": "While instantiating macro {0}: Command ''{1}'' caused error: {2}", "commands.function.instantiationFailure": "Failed to instantiate function {0}: {1}", "commands.function.result": "Function {0} returned {1}", "commands.function.scheduled.multiple": "Running functions {0}", "commands.function.scheduled.no_functions": "Can''t find any functions for name {0}", "commands.function.scheduled.single": "Running function {0}", "commands.function.success.multiple": "Executed {0} command(s) from {1} functions", "commands.function.success.multiple.result": "Executed {0} functions", "commands.function.success.single": "Executed {0} command(s) from function ''{1}''", "commands.function.success.single.result": "Function ''{1}'' returned {0}", "commands.gamemode.success.other": "Set {0}''s game mode to {1}", "commands.gamemode.success.self": "Set own game mode to {0}", "commands.gamerule.query": "<PERSON><PERSON><PERSON> {0} is currently set to: {1}", "commands.gamerule.set": "<PERSON><PERSON><PERSON> {0} is now set to: {1}", "commands.give.failed.toomanyitems": "Can''t give more than {0} of {1}", "commands.give.success.multiple": "Gave {0} {1} to {2} players", "commands.give.success.single": "Gave {0} {1} to {2}", "commands.help.failed": "Unknown command or insufficient permissions", "commands.item.block.set.success": "Replaced a slot at {0}, {1}, {2} with {3}", "commands.item.entity.set.success.multiple": "Replaced a slot on {0} entities with {1}", "commands.item.entity.set.success.single": "Replaced a slot on {0} with {1}", "commands.item.source.no_such_slot": "The source does not have slot {0}", "commands.item.source.not_a_container": "Source position {0}, {1}, {2} is not a container", "commands.item.target.no_changed.known_item": "No targets accepted item {0} into slot {1}", "commands.item.target.no_changes": "No targets accepted item into slot {0}", "commands.item.target.no_such_slot": "The target does not have slot {0}", "commands.item.target.not_a_container": "Target position {0}, {1}, {2} is not a container", "commands.jfr.dump.failed": "Failed to dump JFR recording: {0}", "commands.jfr.start.failed": "Failed to start JFR profiling", "commands.jfr.started": "JFR profiling started", "commands.jfr.stopped": "JFR profiling stopped and dumped to {0}", "commands.kick.owner.failed": "Cannot kick server owner in LAN game", "commands.kick.singleplayer.failed": "<PERSON><PERSON> kick in an offline singleplayer game", "commands.kick.success": "Kicked {0}: {1}", "commands.kill.success.multiple": "Killed {0} entities", "commands.kill.success.single": "Killed {0}", "commands.list.nameAndId": "{0} ({1})", "commands.list.players": "There are {0} of a max of {1} players online: {2}", "commands.locate.biome.not_found": "Could not find a biome of type \"{0}\" within reasonable distance", "commands.locate.biome.success": "The nearest {0} is at {1} ({2} blocks away)", "commands.locate.poi.not_found": "Could not find a point of interest of type \"{0}\" within reasonable distance", "commands.locate.poi.success": "The nearest {0} is at {1} ({2} blocks away)", "commands.locate.structure.invalid": "There is no structure with type \"{0}\"", "commands.locate.structure.not_found": "Could not find a structure of type \"{0}\" nearby", "commands.locate.structure.success": "The nearest {0} is at {1} ({2} blocks away)", "commands.message.display.incoming": "{0} whispers to you: {1}", "commands.message.display.outgoing": "You whisper to {0}: {1}", "commands.op.failed": "Nothing changed. The player already is an operator", "commands.op.success": "Made {0} a server operator", "commands.pardon.failed": "Nothing changed. The player isn''t banned", "commands.pardon.success": "Unbanned {0}", "commands.pardonip.failed": "Nothing changed. That IP isn''t banned", "commands.pardonip.invalid": "Invalid IP address", "commands.pardonip.success": "Unbanned IP {0}", "commands.particle.failed": "The particle was not visible for anybody", "commands.particle.success": "Displaying particle {0}", "commands.perf.alreadyRunning": "The performance profiler is already started", "commands.perf.notRunning": "The performance profiler hasn''t started", "commands.perf.reportFailed": "Failed to create debug report", "commands.perf.reportSaved": "Created debug report in {0}", "commands.perf.started": "Started 10 second performance profiling run (use ''/perf stop'' to stop early)", "commands.perf.stopped": "Stopped performance profiling after {0} second(s) and {1} tick(s) ({2} tick(s) per second)", "commands.place.feature.failed": "Failed to place feature", "commands.place.feature.invalid": "There is no feature with type \"{0}\"", "commands.place.feature.success": "Placed \"{0}\" at {1}, {2}, {3}", "commands.place.jigsaw.failed": "Failed to generate jigsaw", "commands.place.jigsaw.invalid": "There is no template pool with type \"{0}\"", "commands.place.jigsaw.success": "Generated jigsaw at {0}, {1}, {2}", "commands.place.structure.failed": "Failed to place structure", "commands.place.structure.invalid": "There is no structure with type \"{0}\"", "commands.place.structure.success": "Generated structure \"{0}\" at {1}, {2}, {3}", "commands.place.template.failed": "Failed to place template", "commands.place.template.invalid": "There is no template with id \"{0}\"", "commands.place.template.success": "Loaded template \"{0}\" at {1}, {2}, {3}", "commands.playsound.failed": "The sound is too far away to be heard", "commands.playsound.success.multiple": "Played sound {0} to {1} players", "commands.playsound.success.single": "Played sound {0} to {1}", "commands.publish.alreadyPublished": "Multiplayer game is already hosted on port {0}", "commands.publish.failed": "Unable to host local game", "commands.publish.started": "Local game hosted on port {0}", "commands.publish.success": "Multiplayer game is now hosted on port {0}", "commands.random.error.range_too_large": "The range of the random value must be at most 2147483646", "commands.random.error.range_too_small": "The range of the random value must be at least 2", "commands.random.reset.all.success": "Reset {0} random sequence(s)", "commands.random.reset.success": "Reset random sequence {0}", "commands.random.roll": "{0} rolled {1} (from {2} to {3})", "commands.random.sample.success": "Randomized value: {0}", "commands.recipe.give.failed": "No new recipes were learned", "commands.recipe.give.success.multiple": "Unlocked {0} recipes for {1} players", "commands.recipe.give.success.single": "Unlocked {0} recipes for {1}", "commands.recipe.take.failed": "No recipes could be forgotten", "commands.recipe.take.success.multiple": "Took {0} recipes from {1} players", "commands.recipe.take.success.single": "Took {0} recipes from {1}", "commands.reload.failure": "Reload failed; keeping old data", "commands.reload.success": "Reloading!", "commands.ride.already_riding": "{0} is already riding {1}", "commands.ride.dismount.success": "{0} stopped riding {1}", "commands.ride.mount.failure.cant_ride_players": "Players can''t be ridden", "commands.ride.mount.failure.generic": "{0} couldn''t start riding {1}", "commands.ride.mount.failure.loop": "Can''t mount entity on itself or any of its passengers", "commands.ride.mount.failure.wrong_dimension": "Can''t mount entity in different dimension", "commands.ride.mount.success": "{0} started riding {1}", "commands.ride.not_riding": "{0} is not riding any vehicle", "commands.rotate.success": "Rotated {0}", "commands.save.alreadyOff": "Saving is already turned off", "commands.save.alreadyOn": "Saving is already turned on", "commands.save.disabled": "Automatic saving is now disabled", "commands.save.enabled": "Automatic saving is now enabled", "commands.save.failed": "Unable to save the game (is there enough disk space?)", "commands.save.saving": "Saving the game (this may take a moment!)", "commands.save.success": "Saved the game", "commands.schedule.cleared.failure": "No schedules with id {0}", "commands.schedule.cleared.success": "Removed {0} schedule(s) with id {1}", "commands.schedule.created.function": "Scheduled function ''{0}'' in {1} tick(s) at gametime {2}", "commands.schedule.created.tag": "Scheduled tag ''{0}'' in {1} tick(s) at gametime {2}", "commands.schedule.macro": "Can''t schedule a macro", "commands.schedule.same_tick": "Can''t schedule for current tick", "commands.scoreboard.objectives.add.duplicate": "An objective already exists by that name", "commands.scoreboard.objectives.add.success": "Created new objective {0}", "commands.scoreboard.objectives.display.alreadyEmpty": "Nothing changed. That display slot is already empty", "commands.scoreboard.objectives.display.alreadySet": "Nothing changed. That display slot is already showing that objective", "commands.scoreboard.objectives.display.cleared": "Cleared any objectives in display slot {0}", "commands.scoreboard.objectives.display.set": "Set display slot {0} to show objective {1}", "commands.scoreboard.objectives.list.empty": "There are no objectives", "commands.scoreboard.objectives.list.success": "There are {0} objective(s): {1}", "commands.scoreboard.objectives.modify.displayAutoUpdate.disable": "Disabled display auto-update for objective {0}", "commands.scoreboard.objectives.modify.displayAutoUpdate.enable": "Enabled display auto-update for objective {0}", "commands.scoreboard.objectives.modify.displayname": "Changed the display name of {0} to {1}", "commands.scoreboard.objectives.modify.objectiveFormat.clear": "Cleared default number format of objective {0}", "commands.scoreboard.objectives.modify.objectiveFormat.set": "Changed default number format of objective {0}", "commands.scoreboard.objectives.modify.rendertype": "Changed the render type of objective {0}", "commands.scoreboard.objectives.remove.success": "Removed objective {0}", "commands.scoreboard.players.add.success.multiple": "Added {0} to {1} for {2} entities", "commands.scoreboard.players.add.success.single": "Added {0} to {1} for {2} (now {3})", "commands.scoreboard.players.display.name.clear.success.multiple": "Cleared display name for {0} entities in {1}", "commands.scoreboard.players.display.name.clear.success.single": "Cleared display name for {0} in {1}", "commands.scoreboard.players.display.name.set.success.multiple": "Changed display name to {0} for {1} entities in {2}", "commands.scoreboard.players.display.name.set.success.single": "Changed display name to {0} for {1} in {2}", "commands.scoreboard.players.display.numberFormat.clear.success.multiple": "Cleared number format for {0} entities in {1}", "commands.scoreboard.players.display.numberFormat.clear.success.single": "Cleared number format for {0} in {1}", "commands.scoreboard.players.display.numberFormat.set.success.multiple": "Changed number format for {0} entities in {1}", "commands.scoreboard.players.display.numberFormat.set.success.single": "Changed number format for {0} in {1}", "commands.scoreboard.players.enable.failed": "Nothing changed. That trigger is already enabled", "commands.scoreboard.players.enable.invalid": "Enable only works on trigger-objectives", "commands.scoreboard.players.enable.success.multiple": "Enabled trigger {0} for {1} entities", "commands.scoreboard.players.enable.success.single": "Enabled trigger {0} for {1}", "commands.scoreboard.players.get.null": "Can''t get value of {0} for {1}; none is set", "commands.scoreboard.players.get.success": "{0} has {1} {2}", "commands.scoreboard.players.list.empty": "There are no tracked entities", "commands.scoreboard.players.list.entity.empty": "{0} has no scores to show", "commands.scoreboard.players.list.entity.entry": "{0}: {1}", "commands.scoreboard.players.list.entity.success": "{0} has {1} score(s):", "commands.scoreboard.players.list.success": "There are {0} tracked entity/entities: {1}", "commands.scoreboard.players.operation.success.multiple": "Updated {0} for {1} entities", "commands.scoreboard.players.operation.success.single": "Set {0} for {1} to {2}", "commands.scoreboard.players.remove.success.multiple": "Removed {0} from {1} for {2} entities", "commands.scoreboard.players.remove.success.single": "Removed {0} from {1} for {2} (now {3})", "commands.scoreboard.players.reset.all.multiple": "Reset all scores for {0} entities", "commands.scoreboard.players.reset.all.single": "Reset all scores for {0}", "commands.scoreboard.players.reset.specific.multiple": "Reset {0} for {1} entities", "commands.scoreboard.players.reset.specific.single": "Reset {0} for {1}", "commands.scoreboard.players.set.success.multiple": "Set {0} for {1} entities to {2}", "commands.scoreboard.players.set.success.single": "Set {0} for {1} to {2}", "commands.seed.success": "Seed: {0}", "commands.setblock.failed": "Could not set the block", "commands.setblock.success": "Changed the block at {0}, {1}, {2}", "commands.setidletimeout.success": "The player idle timeout is now {0} minute(s)", "commands.setidletimeout.success.disabled": "The player idle timeout is now disabled", "commands.setworldspawn.failure.not_overworld": "Can only set the world spawn for overworld", "commands.setworldspawn.success": "Set the world spawn point to {0}, {1}, {2} [{3}]", "commands.spawnpoint.success.multiple": "Set spawn point to {0}, {1}, {2} [{3}] in {4} for {5} players", "commands.spawnpoint.success.single": "Set spawn point to {0}, {1}, {2} [{3}] in {4} for {5}", "commands.spectate.not_spectator": "{0} is not in spectator mode", "commands.spectate.self": "Cannot spectate yourself", "commands.spectate.success.started": "Now spectating {0}", "commands.spectate.success.stopped": "No longer spectating an entity", "commands.spreadplayers.failed.entities": "Could not spread {0} entity/entities around {1}, {2} (too many entities for space - try using spread of at most {3})", "commands.spreadplayers.failed.invalid.height": "Invalid maxHeight {0}; expected higher than world minimum {1}", "commands.spreadplayers.failed.teams": "Could not spread {0} team(s) around {1}, {2} (too many entities for space - try using spread of at most {3})", "commands.spreadplayers.success.entities": "Spread {0} entity/entities around {1}, {2} with an average distance of {3} block(s) apart", "commands.spreadplayers.success.teams": "Spread {0} team(s) around {1}, {2} with an average distance of {3} block(s) apart", "commands.stop.stopping": "Stopping the server", "commands.stopsound.success.source.any": "Stopped all ''{0}'' sounds", "commands.stopsound.success.source.sound": "Stopped sound ''{0}'' on source ''{1}''", "commands.stopsound.success.sourceless.any": "Stopped all sounds", "commands.stopsound.success.sourceless.sound": "Stopped sound ''{0}''", "commands.summon.failed": "Unable to summon entity", "commands.summon.failed.uuid": "Unable to summon entity due to duplicate UUIDs", "commands.summon.invalidPosition": "Invalid position for summon", "commands.summon.success": "Summoned new {0}", "commands.tag.add.failed": "Target either already has the tag or has too many tags", "commands.tag.add.success.multiple": "Added tag ''{0}'' to {1} entities", "commands.tag.add.success.single": "Added tag ''{0}'' to {1}", "commands.tag.list.multiple.empty": "There are no tags on the {0} entities", "commands.tag.list.multiple.success": "The {0} entities have {1} total tags: {2}", "commands.tag.list.single.empty": "{0} has no tags", "commands.tag.list.single.success": "{0} has {1} tags: {2}", "commands.tag.remove.failed": "Target does not have this tag", "commands.tag.remove.success.multiple": "Removed tag ''{0}'' from {1} entities", "commands.tag.remove.success.single": "Removed tag ''{0}'' from {1}", "commands.team.add.duplicate": "A team already exists by that name", "commands.team.add.success": "Created team {0}", "commands.team.empty.success": "Removed {0} member(s) from team {1}", "commands.team.empty.unchanged": "Nothing changed. That team is already empty", "commands.team.join.success.multiple": "Added {0} members to team {1}", "commands.team.join.success.single": "Added {0} to team {1}", "commands.team.leave.success.multiple": "Removed {0} members from any team", "commands.team.leave.success.single": "Removed {0} from any team", "commands.team.list.members.empty": "There are no members on team {0}", "commands.team.list.members.success": "Team {0} has {1} member(s): {2}", "commands.team.list.teams.empty": "There are no teams", "commands.team.list.teams.success": "There are {0} team(s): {1}", "commands.team.option.collisionRule.success": "Collision rule for team {0} is now \"{1}\"", "commands.team.option.collisionRule.unchanged": "Nothing changed. Collision rule is already that value", "commands.team.option.color.success": "Updated the color for team {0} to {1}", "commands.team.option.color.unchanged": "Nothing changed. That team already has that color", "commands.team.option.deathMessageVisibility.success": "Death message visibility for team {0} is now \"{1}\"", "commands.team.option.deathMessageVisibility.unchanged": "Nothing changed. Death message visibility is already that value", "commands.team.option.friendlyfire.alreadyDisabled": "Nothing changed. Friendly fire is already disabled for that team", "commands.team.option.friendlyfire.alreadyEnabled": "Nothing changed. Friendly fire is already enabled for that team", "commands.team.option.friendlyfire.disabled": "Disabled friendly fire for team {0}", "commands.team.option.friendlyfire.enabled": "Enabled friendly fire for team {0}", "commands.team.option.name.success": "Updated the name of team {0}", "commands.team.option.name.unchanged": "Nothing changed. That team already has that name", "commands.team.option.nametagVisibility.success": "Nametag visibility for team {0} is now \"{1}\"", "commands.team.option.nametagVisibility.unchanged": "Nothing changed. Nametag visibility is already that value", "commands.team.option.prefix.success": "Team prefix set to {0}", "commands.team.option.seeFriendlyInvisibles.alreadyDisabled": "Nothing changed. That team already can''t see invisible teammates", "commands.team.option.seeFriendlyInvisibles.alreadyEnabled": "Nothing changed. That team can already see invisible teammates", "commands.team.option.seeFriendlyInvisibles.disabled": "Team {0} can no longer see invisible teammates", "commands.team.option.seeFriendlyInvisibles.enabled": "Team {0} can now see invisible teammates", "commands.team.option.suffix.success": "Team suffix set to {0}", "commands.team.remove.success": "Removed team {0}", "commands.teammsg.failed.noteam": "You must be on a team to message your team", "commands.teleport.invalidPosition": "Invalid position for teleport", "commands.teleport.success.entity.multiple": "Teleported {0} entities to {1}", "commands.teleport.success.entity.single": "Teleported {0} to {1}", "commands.teleport.success.location.multiple": "Teleported {0} entities to {1}, {2}, {3}", "commands.teleport.success.location.single": "Teleported {0} to {1}, {2}, {3}", "commands.tick.query.percentiles": "Percentiles: P50: {0}ms P95: {1}ms P99: {2}ms, sample: {3}", "commands.tick.query.rate.running": "Target tick rate: {0} per second.\nAverage time per tick: {1}ms (Target: {2}ms)", "commands.tick.query.rate.sprinting": "Target tick rate: {0} per second (ignored, reference only).\nAverage time per tick: {1}ms", "commands.tick.rate.success": "Set the target tick rate to {0} per second", "commands.tick.sprint.report": "Sprint completed with {0} ticks per second, or {1} ms per tick", "commands.tick.sprint.stop.fail": "No tick sprint in progress", "commands.tick.sprint.stop.success": "Interrupted the current tick sprint", "commands.tick.status.frozen": "The game is frozen", "commands.tick.status.lagging": "The game is running, but can''t keep up with the target tick rate", "commands.tick.status.running": "The game is running normally", "commands.tick.status.sprinting": "The game is sprinting", "commands.tick.step.fail": "Unable to step the game - the game must be frozen first", "commands.tick.step.stop.fail": "No tick step in progress", "commands.tick.step.stop.success": "Interrupted the current tick step", "commands.tick.step.success": "Stepping {0} tick(s)", "commands.time.query": "The time is {0}", "commands.time.set": "Set the time to {0}", "commands.title.cleared.multiple": "Cleared titles for {0} players", "commands.title.cleared.single": "Cleared titles for {0}", "commands.title.reset.multiple": "Reset title options for {0} players", "commands.title.reset.single": "Reset title options for {0}", "commands.title.show.actionbar.multiple": "Showing new actionbar title for {0} players", "commands.title.show.actionbar.single": "Showing new actionbar title for {0}", "commands.title.show.subtitle.multiple": "Showing new subtitle for {0} players", "commands.title.show.subtitle.single": "Showing new subtitle for {0}", "commands.title.show.title.multiple": "Showing new title for {0} players", "commands.title.show.title.single": "Showing new title for {0}", "commands.title.times.multiple": "Changed title display times for {0} players", "commands.title.times.single": "Changed title display times for {0}", "commands.transfer.error.no_players": "Must specify at least one player to transfer", "commands.transfer.success.multiple": "Transferring {0} players to {1}:{2}", "commands.transfer.success.single": "Transferring {0} to {1}:{2}", "commands.trigger.add.success": "Triggered {0} (added {1} to value)", "commands.trigger.failed.invalid": "You can only trigger objectives that are ''trigger'' type", "commands.trigger.failed.unprimed": "You cannot trigger this objective yet", "commands.trigger.set.success": "Triggered {0} (set value to {1})", "commands.trigger.simple.success": "Triggered {0}", "commands.weather.set.clear": "Set the weather to clear", "commands.weather.set.rain": "Set the weather to rain", "commands.weather.set.thunder": "Set the weather to rain & thunder", "commands.whitelist.add.failed": "Player is already whitelisted", "commands.whitelist.add.success": "Added {0} to the whitelist", "commands.whitelist.alreadyOff": "Whitelist is already turned off", "commands.whitelist.alreadyOn": "Whitelist is already turned on", "commands.whitelist.disabled": "Whitelist is now turned off", "commands.whitelist.enabled": "Whitelist is now turned on", "commands.whitelist.list": "There are {0} whitelisted player(s): {1}", "commands.whitelist.none": "There are no whitelisted players", "commands.whitelist.reloaded": "Reloaded the whitelist", "commands.whitelist.remove.failed": "Player is not whitelisted", "commands.whitelist.remove.success": "Removed {0} from the whitelist", "commands.worldborder.center.failed": "Nothing changed. The world border is already centered there", "commands.worldborder.center.success": "Set the center of the world border to {0}, {1}", "commands.worldborder.damage.amount.failed": "Nothing changed. The world border damage is already that amount", "commands.worldborder.damage.amount.success": "Set the world border damage to {0} per block each second", "commands.worldborder.damage.buffer.failed": "Nothing changed. The world border damage buffer is already that distance", "commands.worldborder.damage.buffer.success": "Set the world border damage buffer to {0} block(s)", "commands.worldborder.get": "The world border is currently {0} block(s) wide", "commands.worldborder.set.failed.big": "World border cannot be bigger than {0} blocks wide", "commands.worldborder.set.failed.far": "World border cannot be further out than {0} blocks", "commands.worldborder.set.failed.nochange": "Nothing changed. The world border is already that size", "commands.worldborder.set.failed.small": "World border cannot be smaller than 1 block wide", "commands.worldborder.set.grow": "Growing the world border to {0} blocks wide over {1} seconds", "commands.worldborder.set.immediate": "Set the world border to {0} block(s) wide", "commands.worldborder.set.shrink": "Shrinking the world border to {0} block(s) wide over {1} second(s)", "commands.worldborder.warning.distance.failed": "Nothing changed. The world border warning is already that distance", "commands.worldborder.warning.distance.success": "Set the world border warning distance to {0} block(s)", "commands.worldborder.warning.time.failed": "Nothing changed. The world border warning is already that amount of time", "commands.worldborder.warning.time.success": "Set the world border warning time to {0} second(s)", "death.attack.anvil": "{0} was squashed by a falling anvil", "death.attack.anvil.player": "{0} was squashed by a falling anvil while fighting {1}", "death.attack.arrow": "{0} was shot by {1}", "death.attack.arrow.item": "{0} was shot by {1} using {2}", "death.attack.badRespawnPoint.link": "Intentional Game Design", "death.attack.badRespawnPoint.message": "{0} was killed by {1}", "death.attack.cactus": "{0} was pricked to death", "death.attack.cactus.player": "{0} walked into a cactus while trying to escape {1}", "death.attack.cramming": "{0} was squished too much", "death.attack.cramming.player": "{0} was squashed by {1}", "death.attack.dragonBreath": "{0} was roasted in dragon''s breath", "death.attack.dragonBreath.player": "{0} was roasted in dragon''s breath by {1}", "death.attack.drown": "{0} drowned", "death.attack.drown.player": "{0} drowned while trying to escape {1}", "death.attack.dryout": "{0} died from dehydration", "death.attack.dryout.player": "{0} died from dehydration while trying to escape {1}", "death.attack.even_more_magic": "{0} was killed by even more magic", "death.attack.explosion": "{0} blew up", "death.attack.explosion.player": "{0} was blown up by {1}", "death.attack.explosion.player.item": "{0} was blown up by {1} using {2}", "death.attack.fall": "{0} hit the ground too hard", "death.attack.fall.player": "{0} hit the ground too hard while trying to escape {1}", "death.attack.fallingBlock": "{0} was squashed by a falling block", "death.attack.fallingBlock.player": "{0} was squashed by a falling block while fighting {1}", "death.attack.fallingStalactite": "{0} was skewered by a falling stalactite", "death.attack.fallingStalactite.player": "{0} was skewered by a falling stalactite while fighting {1}", "death.attack.fireball": "{0} was fireballed by {1}", "death.attack.fireball.item": "{0} was fireballed by {1} using {2}", "death.attack.fireworks": "{0} went off with a bang", "death.attack.fireworks.item": "{0} went off with a bang due to a firework fired from {2} by {1}", "death.attack.fireworks.player": "{0} went off with a bang while fighting {1}", "death.attack.flyIntoWall": "{0} experienced kinetic energy", "death.attack.flyIntoWall.player": "{0} experienced kinetic energy while trying to escape {1}", "death.attack.freeze": "{0} froze to death", "death.attack.freeze.player": "{0} was frozen to death by {1}", "death.attack.generic": "{0} died", "death.attack.generic.player": "{0} died because of {1}", "death.attack.genericKill": "{0} was killed", "death.attack.genericKill.player": "{0} was killed while fighting {1}", "death.attack.hotFloor": "{0} discovered the floor was lava", "death.attack.hotFloor.player": "{0} walked into the danger zone due to {1}", "death.attack.indirectMagic": "{0} was killed by {1} using magic", "death.attack.indirectMagic.item": "{0} was killed by {1} using {2}", "death.attack.inFire": "{0} went up in flames", "death.attack.inFire.player": "{0} walked into fire while fighting {1}", "death.attack.inWall": "{0} suffocated in a wall", "death.attack.inWall.player": "{0} suffocated in a wall while fighting {1}", "death.attack.lava": "{0} tried to swim in lava", "death.attack.lava.player": "{0} tried to swim in lava to escape {1}", "death.attack.lightningBolt": "{0} was struck by lightning", "death.attack.lightningBolt.player": "{0} was struck by lightning while fighting {1}", "death.attack.mace_smash": "{0} was smashed by {1}", "death.attack.mace_smash.item": "{0} was smashed by {1} with {2}", "death.attack.magic": "{0} was killed by magic", "death.attack.magic.player": "{0} was killed by magic while trying to escape {1}", "death.attack.message_too_long": "Actually, the message was too long to deliver fully. Sorry! Here''s a stripped version: {0}", "death.attack.mob": "{0} was slain by {1}", "death.attack.mob.item": "{0} was slain by {1} using {2}", "death.attack.onFire": "{0} burned to death", "death.attack.onFire.item": "{0} was burned to a crisp while fighting {1} wielding {2}", "death.attack.onFire.player": "{0} was burned to a crisp while fighting {1}", "death.attack.outOfWorld": "{0} fell out of the world", "death.attack.outOfWorld.player": "{0} didn''t want to live in the same world as {1}", "death.attack.outsideBorder": "{0} left the confines of this world", "death.attack.outsideBorder.player": "{0} left the confines of this world while fighting {1}", "death.attack.player": "{0} was slain by {1}", "death.attack.player.item": "{0} was slain by {1} using {2}", "death.attack.sonic_boom": "{0} was obliterated by a sonically-charged shriek", "death.attack.sonic_boom.item": "{0} was obliterated by a sonically-charged shriek while trying to escape {1} wielding {2}", "death.attack.sonic_boom.player": "{0} was obliterated by a sonically-charged shriek while trying to escape {1}", "death.attack.stalagmite": "{0} was impaled on a stalagmite", "death.attack.stalagmite.player": "{0} was impaled on a stalagmite while fighting {1}", "death.attack.starve": "{0} starved to death", "death.attack.starve.player": "{0} starved to death while fighting {1}", "death.attack.sting": "{0} was stung to death", "death.attack.sting.item": "{0} was stung to death by {1} using {2}", "death.attack.sting.player": "{0} was stung to death by {1}", "death.attack.sweetBerryBush": "{0} was poked to death by a sweet berry bush", "death.attack.sweetBerryBush.player": "{0} was poked to death by a sweet berry bush while trying to escape {1}", "death.attack.thorns": "{0} was killed while trying to hurt {1}", "death.attack.thorns.item": "{0} was killed by {2} while trying to hurt {1}", "death.attack.thrown": "{0} was pummeled by {1}", "death.attack.thrown.item": "{0} was pummeled by {1} using {2}", "death.attack.trident": "{0} was impaled by {1}", "death.attack.trident.item": "{0} was impaled by {1} with {2}", "death.attack.wither": "{0} withered away", "death.attack.wither.player": "{0} withered away while fighting {1}", "death.attack.witherSkull": "{0} was shot by a skull from {1}", "death.attack.witherSkull.item": "{0} was shot by a skull from {1} using {2}", "death.fell.accident.generic": "{0} fell from a high place", "death.fell.accident.ladder": "{0} fell off a ladder", "death.fell.accident.other_climbable": "{0} fell while climbing", "death.fell.accident.scaffolding": "{0} fell off scaffolding", "death.fell.accident.twisting_vines": "{0} fell off some twisting vines", "death.fell.accident.vines": "{0} fell off some vines", "death.fell.accident.weeping_vines": "{0} fell off some weeping vines", "death.fell.assist": "{0} was doomed to fall by {1}", "death.fell.assist.item": "{0} was doomed to fall by {1} using {2}", "death.fell.finish": "{0} fell too far and was finished by {1}", "death.fell.finish.item": "{0} fell too far and was finished by {1} using {2}", "death.fell.killer": "{0} was doomed to fall", "disconnect.endOfStream": "End of stream", "disconnect.exceeded_packet_rate": "Kicked for exceeding packet rate limit", "disconnect.genericReason": "{0}", "disconnect.ignoring_status_request": "Ignoring status request", "disconnect.loginFailedInfo": "Failed to log in: {0}", "disconnect.loginFailedInfo.insufficientPrivileges": "Multiplayer is disabled. Please check your Microsoft account settings.", "disconnect.loginFailedInfo.invalidSession": "Invalid session (Try restarting your game and the launcher)", "disconnect.loginFailedInfo.serversUnavailable": "The authentication servers are currently not reachable. Please try again.", "disconnect.loginFailedInfo.userBanned": "You are banned from playing online", "disconnect.lost": "Connection Lost", "disconnect.packetError": "Network Protocol Error", "disconnect.spam": "Kicked for spamming", "disconnect.timeout": "Timed out", "disconnect.transfer": "Transferred to another server", "disconnect.unknownHost": "Unknown host", "entity.minecraft.acacia_boat": "Acacia Boat", "entity.minecraft.acacia_chest_boat": "Acacia Boat with Chest", "entity.minecraft.allay": "Allay", "entity.minecraft.area_effect_cloud": "Area Effect Cloud", "entity.minecraft.armadillo": "Armadillo", "entity.minecraft.armor_stand": "Armor Stand", "entity.minecraft.arrow": "Arrow", "entity.minecraft.axolotl": "Axolotl", "entity.minecraft.bamboo_chest_raft": "Bamboo Raft with Chest", "entity.minecraft.bamboo_raft": "Bamboo Raft", "entity.minecraft.bat": "Bat", "entity.minecraft.bee": "Bee", "entity.minecraft.birch_boat": "<PERSON> Boat", "entity.minecraft.birch_chest_boat": "<PERSON> Boat with Chest", "entity.minecraft.blaze": "Blaze", "entity.minecraft.block_display": "Block Display", "entity.minecraft.boat": "Boat", "entity.minecraft.bogged": "Bogged", "entity.minecraft.breeze": "<PERSON><PERSON>", "entity.minecraft.breeze_wind_charge": "Wind Charge", "entity.minecraft.camel": "Camel", "entity.minecraft.cat": "Cat", "entity.minecraft.cave_spider": "<PERSON> Spider", "entity.minecraft.cherry_boat": "Cherry Boat", "entity.minecraft.cherry_chest_boat": "Cherry Boat with Chest", "entity.minecraft.chest_boat": "Boat with Chest", "entity.minecraft.chest_minecart": "Minecart with Chest", "entity.minecraft.chicken": "Chicken", "entity.minecraft.cod": "Cod", "entity.minecraft.command_block_minecart": "Minecart with Command Block", "entity.minecraft.cow": "Cow", "entity.minecraft.creaking": "Creaking", "entity.minecraft.creaking_transient": "Creaking", "entity.minecraft.creeper": "C<PERSON>per", "entity.minecraft.dark_oak_boat": "Dark Oak Boat", "entity.minecraft.dark_oak_chest_boat": "Dark Oak Boat with Chest", "entity.minecraft.dolphin": "Dolphin", "entity.minecraft.donkey": "<PERSON><PERSON>", "entity.minecraft.dragon_fireball": "Dragon Fireball", "entity.minecraft.drowned": "Drowned", "entity.minecraft.egg": "Thrown Egg", "entity.minecraft.elder_guardian": "Elder Guardian", "entity.minecraft.end_crystal": "End Crystal", "entity.minecraft.ender_dragon": "<PERSON><PERSON>", "entity.minecraft.ender_pearl": "<PERSON><PERSON><PERSON> <PERSON>", "entity.minecraft.enderman": "<PERSON><PERSON>", "entity.minecraft.endermite": "Endermite", "entity.minecraft.evoker": "Evoker", "entity.minecraft.evoker_fangs": "Evoker <PERSON>s", "entity.minecraft.experience_bottle": "Thrown Bottle o'' Enchanting", "entity.minecraft.experience_orb": "Experience Orb", "entity.minecraft.eye_of_ender": "Eye of <PERSON>er", "entity.minecraft.falling_block": "Falling Block", "entity.minecraft.falling_block_type": "Falling {0}", "entity.minecraft.fireball": "Fireball", "entity.minecraft.firework_rocket": "Firework Rocket", "entity.minecraft.fishing_bobber": "Fishing Bobber", "entity.minecraft.fox": "Fox", "entity.minecraft.frog": "<PERSON>", "entity.minecraft.furnace_minecart": "Minecart with Furnace", "entity.minecraft.ghast": "<PERSON><PERSON><PERSON>", "entity.minecraft.giant": "Giant", "entity.minecraft.glow_item_frame": "G<PERSON> Item <PERSON>", "entity.minecraft.glow_squid": "Glow Squid", "entity.minecraft.goat": "Goa<PERSON>", "entity.minecraft.guardian": "Guardian", "entity.minecraft.hoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.hopper_minecart": "Minecart with <PERSON>", "entity.minecraft.horse": "Horse", "entity.minecraft.husk": "Husk", "entity.minecraft.illusioner": "<PERSON><PERSON><PERSON>", "entity.minecraft.interaction": "Interaction", "entity.minecraft.iron_golem": "Iron Golem", "entity.minecraft.item": "<PERSON><PERSON>", "entity.minecraft.item_display": "<PERSON><PERSON>", "entity.minecraft.item_frame": "<PERSON><PERSON>", "entity.minecraft.jungle_boat": "Jungle Boat", "entity.minecraft.jungle_chest_boat": "Jungle Boat with Chest", "entity.minecraft.killer_bunny": "The Killer Bunny", "entity.minecraft.leash_knot": "<PERSON><PERSON>", "entity.minecraft.lightning_bolt": "Lightning Bolt", "entity.minecraft.llama": "Llama", "entity.minecraft.llama_spit": "Llama <PERSON>", "entity.minecraft.magma_cube": "Magma Cube", "entity.minecraft.mangrove_boat": "Mangrove Boat", "entity.minecraft.mangrove_chest_boat": "Mangrove Boat with Chest", "entity.minecraft.marker": "<PERSON><PERSON>", "entity.minecraft.minecart": "Minecart", "entity.minecraft.mooshroom": "Mooshroom", "entity.minecraft.mule": "<PERSON><PERSON>", "entity.minecraft.oak_boat": "Oak Boat", "entity.minecraft.oak_chest_boat": "Oak Boat with Chest", "entity.minecraft.ocelot": "Ocelot", "entity.minecraft.ominous_item_spawner": "Ominous <PERSON>em <PERSON>wner", "entity.minecraft.painting": "Painting", "entity.minecraft.pale_oak_boat": "Pale Oak Boat", "entity.minecraft.pale_oak_chest_boat": "<PERSON>le Oak Boat with Chest", "entity.minecraft.panda": "Panda", "entity.minecraft.parrot": "<PERSON><PERSON><PERSON>", "entity.minecraft.phantom": "Phantom", "entity.minecraft.pig": "Pig", "entity.minecraft.piglin": "<PERSON><PERSON>", "entity.minecraft.piglin_brute": "<PERSON><PERSON> B<PERSON>", "entity.minecraft.pillager": "Pillager", "entity.minecraft.player": "Player", "entity.minecraft.polar_bear": "Polar Bear", "entity.minecraft.potion": "Potion", "entity.minecraft.pufferfish": "Pufferfish", "entity.minecraft.rabbit": "Rabbit", "entity.minecraft.ravager": "<PERSON><PERSON><PERSON>", "entity.minecraft.salmon": "Salmon", "entity.minecraft.sheep": "Sheep", "entity.minecraft.shulker": "<PERSON><PERSON><PERSON>", "entity.minecraft.shulker_bullet": "<PERSON><PERSON><PERSON> Bullet", "entity.minecraft.silverfish": "Silverfish", "entity.minecraft.skeleton": "Skeleton", "entity.minecraft.skeleton_horse": "Skeleton Horse", "entity.minecraft.slime": "Slime", "entity.minecraft.small_fireball": "Small Fireball", "entity.minecraft.sniffer": "<PERSON><PERSON><PERSON>", "entity.minecraft.snow_golem": "Snow Golem", "entity.minecraft.snowball": "Snowball", "entity.minecraft.spawner_minecart": "<PERSON><PERSON><PERSON> with <PERSON> Spawner", "entity.minecraft.spectral_arrow": "Spectral Arrow", "entity.minecraft.spider": "Spider", "entity.minecraft.spruce_boat": "Spruce Boat", "entity.minecraft.spruce_chest_boat": "Spruce Boat with Chest", "entity.minecraft.squid": "Squid", "entity.minecraft.stray": "Stray", "entity.minecraft.strider": "Strider", "entity.minecraft.tadpole": "Tadpole", "entity.minecraft.text_display": "Text Display", "entity.minecraft.tnt": "Primed TNT", "entity.minecraft.tnt_minecart": "Minecart with TNT", "entity.minecraft.trader_llama": "Trader <PERSON><PERSON><PERSON>", "entity.minecraft.trident": "Trident", "entity.minecraft.tropical_fish": "Tropical Fish", "entity.minecraft.tropical_fish.predefined.0": "Anemone", "entity.minecraft.tropical_fish.predefined.1": "Black Tang", "entity.minecraft.tropical_fish.predefined.2": "Blue Tang", "entity.minecraft.tropical_fish.predefined.3": "Butterflyfish", "entity.minecraft.tropical_fish.predefined.4": "Cichlid", "entity.minecraft.tropical_fish.predefined.5": "Clownfish", "entity.minecraft.tropical_fish.predefined.6": "Cotton Candy <PERSON>", "entity.minecraft.tropical_fish.predefined.7": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.predefined.8": "Emperor <PERSON>", "entity.minecraft.tropical_fish.predefined.9": "Goatfish", "entity.minecraft.tropical_fish.predefined.10": "Moorish Idol", "entity.minecraft.tropical_fish.predefined.11": "Ornate Butterflyfish", "entity.minecraft.tropical_fish.predefined.12": "Parrotfish", "entity.minecraft.tropical_fish.predefined.13": "Queen Angelfish", "entity.minecraft.tropical_fish.predefined.14": "Red Cichlid", "entity.minecraft.tropical_fish.predefined.15": "Red Lipped Blenny", "entity.minecraft.tropical_fish.predefined.16": "Red Snapper", "entity.minecraft.tropical_fish.predefined.17": "Threadfin", "entity.minecraft.tropical_fish.predefined.18": "Tomato Clownfish", "entity.minecraft.tropical_fish.predefined.19": "Triggerfish", "entity.minecraft.tropical_fish.predefined.20": "Yellowtail Parrotfish", "entity.minecraft.tropical_fish.predefined.21": "Yellow Tang", "entity.minecraft.tropical_fish.type.betty": "<PERSON>", "entity.minecraft.tropical_fish.type.blockfish": "Blockfish", "entity.minecraft.tropical_fish.type.brinely": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.clayfish": "Clayfish", "entity.minecraft.tropical_fish.type.dasher": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.flopper": "<PERSON><PERSON><PERSON>", "entity.minecraft.tropical_fish.type.glitter": "Glitter", "entity.minecraft.tropical_fish.type.kob": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.snooper": "Snooper", "entity.minecraft.tropical_fish.type.spotty": "Spotty", "entity.minecraft.tropical_fish.type.stripey": "<PERSON><PERSON>", "entity.minecraft.tropical_fish.type.sunstreak": "Sunstreak", "entity.minecraft.turtle": "Turtle", "entity.minecraft.vex": "Vex", "entity.minecraft.villager": "Villager", "entity.minecraft.villager.armorer": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.butcher": "<PERSON>", "entity.minecraft.villager.cartographer": "Cartographer", "entity.minecraft.villager.cleric": "Cleric", "entity.minecraft.villager.farmer": "<PERSON>", "entity.minecraft.villager.fisherman": "Fisherman", "entity.minecraft.villager.fletcher": "<PERSON>", "entity.minecraft.villager.leatherworker": "Leatherworker", "entity.minecraft.villager.librarian": "Librarian", "entity.minecraft.villager.mason": "<PERSON>", "entity.minecraft.villager.nitwit": "<PERSON><PERSON><PERSON><PERSON>", "entity.minecraft.villager.none": "Villager", "entity.minecraft.villager.shepherd": "<PERSON>", "entity.minecraft.villager.toolsmith": "<PERSON><PERSON><PERSON>", "entity.minecraft.villager.weaponsmith": "Weaponsmith", "entity.minecraft.vindicator": "Vindicator", "entity.minecraft.wandering_trader": "Wandering Trader", "entity.minecraft.warden": "Warden", "entity.minecraft.wind_charge": "Wind Charge", "entity.minecraft.witch": "Witch", "entity.minecraft.wither": "<PERSON>er", "entity.minecraft.wither_skeleton": "<PERSON><PERSON>", "entity.minecraft.wither_skull": "<PERSON><PERSON>", "entity.minecraft.wolf": "<PERSON>", "entity.minecraft.zoglin": "<PERSON><PERSON><PERSON>", "entity.minecraft.zombie": "Zombie", "entity.minecraft.zombie_horse": "Zombie Horse", "entity.minecraft.zombie_villager": "Zombie Villager", "entity.minecraft.zombified_piglin": "Zombified Piglin", "entity.not_summonable": "Can''t summon entity of type {0}", "gameMode.adventure": "Adventure Mode", "gameMode.changed": "Your game mode has been updated to {0}", "gameMode.creative": "Creative Mode", "gameMode.hardcore": "Hardcore Mode!", "gameMode.spectator": "Spectator Mode", "gameMode.survival": "Survival Mode", "item_modifier.unknown": "Unknown item modifier: {0}", "multiplayer.applyingPack": "Applying resource pack", "multiplayer.disconnect.authservers_down": "Authentication servers are down. Please try again later, sorry!", "multiplayer.disconnect.banned": "You are banned from this server", "multiplayer.disconnect.banned_ip.expiration": "\nYour ban will be removed on {0}", "multiplayer.disconnect.banned_ip.reason": "Your IP address is banned from this server.\nReason: {0}", "multiplayer.disconnect.banned.expiration": "\nYour ban will be removed on {0}", "multiplayer.disconnect.banned.reason": "You are banned from this server.\nReason: {0}", "multiplayer.disconnect.chat_validation_failed": "Chat message validation failure", "multiplayer.disconnect.duplicate_login": "You logged in from another location", "multiplayer.disconnect.expired_public_key": "Expired profile public key. Check that your system time is synchronized, and try restarting your game.", "multiplayer.disconnect.flying": "Flying is not enabled on this server", "multiplayer.disconnect.generic": "Disconnected", "multiplayer.disconnect.idling": "You have been idle for too long!", "multiplayer.disconnect.illegal_characters": "Illegal characters in chat", "multiplayer.disconnect.incompatible": "Incompatible client! Please use {0}", "multiplayer.disconnect.invalid_entity_attacked": "Attempting to attack an invalid entity", "multiplayer.disconnect.invalid_packet": "Server sent an invalid packet", "multiplayer.disconnect.invalid_player_data": "Invalid player data", "multiplayer.disconnect.invalid_player_movement": "Invalid move player packet received", "multiplayer.disconnect.invalid_public_key_signature": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_public_key_signature.new": "Invalid signature for profile public key.\nTry restarting your game.", "multiplayer.disconnect.invalid_vehicle_movement": "Invalid move vehicle packet received", "multiplayer.disconnect.ip_banned": "You have been IP banned from this server", "multiplayer.disconnect.kicked": "Kicked by an operator", "multiplayer.disconnect.missing_tags": "Incomplete set of tags received from server.\nPlease contact server operator.", "multiplayer.disconnect.name_taken": "That name is already taken", "multiplayer.disconnect.not_whitelisted": "You are not white-listed on this server!", "multiplayer.disconnect.out_of_order_chat": "Out-of-order chat packet received. Did your system time change?", "multiplayer.disconnect.outdated_client": "Incompatible client! Please use {0}", "multiplayer.disconnect.outdated_server": "Incompatible client! Please use {0}", "multiplayer.disconnect.server_full": "The server is full!", "multiplayer.disconnect.server_shutdown": "Server closed", "multiplayer.disconnect.slow_login": "Took too long to log in", "multiplayer.disconnect.too_many_pending_chats": "Too many unacknowledged chat messages", "multiplayer.disconnect.transfers_disabled": "Server does not accept transfers", "multiplayer.disconnect.unexpected_query_response": "Unexpected custom data from client", "multiplayer.disconnect.unsigned_chat": "Received chat packet with missing or invalid signature.", "multiplayer.disconnect.unverified_username": "Failed to verify username!", "multiplayer.downloadingStats": "Retrieving statistics...", "multiplayer.downloadingTerrain": "Loading terrain...", "multiplayer.lan.server_found": "New server found: {0}", "multiplayer.message_not_delivered": "Can''t deliver chat message, check server logs: {0}", "multiplayer.player.joined": "{0} joined the game", "multiplayer.player.joined.renamed": "{0} (formerly known as {1}) joined the game", "multiplayer.player.left": "{0} left the game", "multiplayer.player.list.hp": "{0}hp", "multiplayer.player.list.narration": "Online players: {0}", "multiplayer.requiredTexturePrompt.disconnect": "Server requires a custom resource pack", "multiplayer.requiredTexturePrompt.line1": "This server requires the use of a custom resource pack.", "multiplayer.requiredTexturePrompt.line2": "Rejecting this custom resource pack will disconnect you from this server.", "multiplayer.socialInteractions.not_available": "Social Interactions are only available in Multiplayer worlds", "multiplayer.status.and_more": "... and {0} more ...", "multiplayer.status.cancelled": "Cancelled", "multiplayer.status.cannot_connect": "Can''t connect to server", "multiplayer.status.cannot_resolve": "Can''t resolve hostname", "multiplayer.status.finished": "Finished", "multiplayer.status.incompatible": "Incompatible version!", "multiplayer.status.motd.narration": "Message of the day: {0}", "multiplayer.status.no_connection": "(no connection)", "multiplayer.status.old": "Old", "multiplayer.status.online": "Online", "multiplayer.status.ping": "{0} ms", "multiplayer.status.ping.narration": "Ping {0} milliseconds", "multiplayer.status.pinging": "Pinging...", "multiplayer.status.player_count": "{0}/{1}", "multiplayer.status.player_count.narration": "{0} out of {1} players online", "multiplayer.status.quitting": "Quitting", "multiplayer.status.request_handled": "Status request has been handled", "multiplayer.status.unknown": "???", "multiplayer.status.unrequested": "Received unrequested status", "multiplayer.status.version.narration": "Server version: {0}", "multiplayer.stopSleeping": "Leave Bed", "multiplayer.texturePrompt.failure.line1": "Server resource pack couldn''t be applied", "multiplayer.texturePrompt.failure.line2": "Any functionality that requires custom resources might not work as expected", "multiplayer.texturePrompt.line1": "This server recommends the use of a custom resource pack.", "multiplayer.texturePrompt.line2": "Would you like to download and install it automagically?", "multiplayer.texturePrompt.serverPrompt": "{0}\n\nMessage from server:\n{1}", "multiplayer.title": "Play Multiplayer", "multiplayer.unsecureserver.toast": "Messages sent on this server may be modified and might not reflect the original message", "multiplayer.unsecureserver.toast.title": "Chat messages can''t be verified", "parsing.bool.expected": "Expected boolean", "parsing.bool.invalid": "Invalid boolean, expected ''true'' or ''false'' but found ''{0}''", "parsing.double.expected": "Expected double", "parsing.double.invalid": "Invalid double ''{0}''", "parsing.expected": "Expected ''{0}''", "parsing.float.expected": "Expected float", "parsing.float.invalid": "Invalid float ''{0}''", "parsing.int.expected": "Expected integer", "parsing.int.invalid": "Invalid integer ''{0}''", "parsing.long.expected": "Expected long", "parsing.long.invalid": "Invalid long ''{0}''", "parsing.quote.escape": "Invalid escape sequence ''\\{0}'' in quoted string", "parsing.quote.expected.end": "Unclosed quoted string", "parsing.quote.expected.start": "Expected quote to start a string", "particle.invalidOptions": "Can''t parse particle options: {0}", "particle.notFound": "Unknown particle: {0}", "recipe.notFound": "Unknown recipe: {0}", "recipe.toast.description": "Check your recipe book", "recipe.toast.title": "New Recipes Unlocked!", "record.nowPlaying": "Now Playing: {0}", "sleep.not_possible": "No amount of rest can pass this night", "sleep.players_sleeping": "{0}/{1} players sleeping", "sleep.skipping_night": "Sleeping through this night", "slot.only_single_allowed": "Only single slots allowed, got ''{0}''", "slot.unknown": "Unknown slot ''{0}''"}