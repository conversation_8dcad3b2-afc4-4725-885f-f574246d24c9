<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration>

<configuration scan="false">
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <import class="ch.qos.logback.classic.encoder.PatternLayoutEncoder"/>
    <import class="ch.qos.logback.classic.filter.ThresholdFilter"/>
    <import class="ch.qos.logback.classic.filter.LevelFilter"/>
    <import class="ch.qos.logback.core.ConsoleAppender"/>
    <import class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"/>

    <statusListener class="com.zenith.terminal.logback.PrintOnlyErrorLogbackStatusListener"/>
    <!-- Prevent logback compression failing during JVM stop https://logback.qos.ch/manual/configuration.html#shutdownHook -->
    <shutdownHook/>

    <appender name="FILE" class="com.zenith.terminal.logback.LazyInitRollingFileAppender">
        <file>log/latest.log</file>
        <encoder class="com.zenith.terminal.logback.AnsiStripEncoder">
            <pattern>[%d{yyyy/MM/dd HH:mm:ss}] [%logger{36}] [%level] %stripAnsi%n</pattern>
        </encoder>
        <rollingPolicy class="TimeBasedRollingPolicy">
            <fileNamePattern>log/proxy-%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <maxHistory>1000</maxHistory>
            <totalSizeCap>250MB</totalSizeCap>
            <TimeBasedFileNamingAndTriggeringPolicy class="com.zenith.terminal.logback.StartupSizeAndTimeBasedTriggeringPolicy">
                <MaxFileSize>250MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <filter class="com.zenith.terminal.logback.LogSourceFilter"/>
        <filter class="ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <appender name="DEBUG_FILE" class="com.zenith.terminal.logback.LazyInitRollingFileAppender">
        <file>log/debug.log</file>
        <encoder class="com.zenith.terminal.logback.AnsiStripEncoder">
            <pattern>[%d{yyyy/MM/dd HH:mm:ss}] [%logger{36}] [%level] %stripAnsi%n</pattern>
        </encoder>
        <rollingPolicy class="TimeBasedRollingPolicy">
            <fileNamePattern>log/proxy-%d{yyyy-MM-dd}.%i.debug.log.zip</fileNamePattern>
            <maxHistory>1000</maxHistory>
            <totalSizeCap>250MB</totalSizeCap>
            <TimeBasedFileNamingAndTriggeringPolicy class="com.zenith.terminal.logback.StartupSizeAndTimeBasedTriggeringPolicy">
                <MaxFileSize>250MB</MaxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <filter class="com.zenith.terminal.logback.LogSourceFilter"/>
        <filter class="ThresholdFilter">
            <level>TRACE</level>
        </filter>
        <filter class="com.zenith.terminal.logback.DebugLogConfigurationFilter"/>
    </appender>
    <appender name="ASYNC_DEBUG_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="DEBUG_FILE"/>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <appender name="TERMINAL" class="com.zenith.terminal.logback.TerminalConsoleAppender">
        <encoder>
            <pattern>[%d{yyyy/MM/dd HH:mm:ss}] [%logger{36}] [%level] %msg%n</pattern>
        </encoder>
        <filter class="com.zenith.terminal.logback.LogSourceFilter"/>
        <filter class="com.zenith.terminal.logback.TerminalDebugLogFilter"/>
    </appender>
    <appender name="ASYNC_TERMINAL" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="TERMINAL"/>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <root>
        <appender-ref ref="ASYNC_TERMINAL"/>
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ASYNC_DEBUG_FILE"/>
    </root>
</configuration>
