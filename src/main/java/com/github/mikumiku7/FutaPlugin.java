package com.github.mikumiku7;

import com.github.mikumiku7.command.AutoChestManagerCommand;
import com.github.mikumiku7.command.ItemSorterCommand;
import com.github.mikumiku7.config.FutaConfig;
import com.github.mikumiku7.module.AutoChestManagerModule;
import com.github.mikumiku7.module.ItemSorterModule;
import com.zenith.plugin.api.Plugin;
import com.zenith.plugin.api.PluginAPI;
import com.zenith.plugin.api.ZenithProxyPlugin;
import net.kyori.adventure.text.logger.slf4j.ComponentLogger;

@Plugin(
        id = "futa-manager",
        version = BuildConstants.VERSION,
        description = "futa经理",
        url = "https://github.com/mikumiku7",
        authors = {"mikumiku7"},
        mcVersions = {"1.21.4"} // to indicate any MC version: @Plugin(mcVersions = "*")
        // if you touch packet classes, you almost certainly need to pin to a single mc version
)
public class FutaPlugin implements ZenithProxyPlugin {
    // public static for simple access from modules and commands
    // or alternatively, you could pass these around in constructors
    public static FutaConfig PLUGIN_CONFIG;
    public static ComponentLogger log;

    @Override
    public void onLoad(PluginAPI pluginAPI) {
        log = pluginAPI.getLogger();
        log.info("futa经理插件加载...");
        // initialize any configurations before modules or commands might need to read them
        PLUGIN_CONFIG = pluginAPI.registerConfig("futa-plugin", FutaConfig.class);

        pluginAPI.registerModule(new AutoChestManagerModule());
        pluginAPI.registerModule(new ItemSorterModule());

        pluginAPI.registerCommand(new AutoChestManagerCommand());
        pluginAPI.registerCommand(new ItemSorterCommand());

        pluginAPI.getLogger().info("箱子管理插件已加载");

    }
}
