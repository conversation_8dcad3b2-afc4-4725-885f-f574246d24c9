package com.github.mikumiku7.command;

import com.github.mikumiku7.FutaPlugin;
import com.github.mikumiku7.config.AutoChestManagerConfig;
import com.github.mikumiku7.module.AutoChestManagerModule;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.zenith.command.api.Command;
import com.zenith.command.api.CommandCategory;
import com.zenith.command.api.CommandContext;
import com.zenith.command.api.CommandUsage;
import com.zenith.discord.Embed;

import static com.zenith.Globals.CACHE;
import static com.zenith.Globals.MODULE;
import static com.zenith.command.brigadier.CustomStringArgumentType.getString;
import static com.zenith.command.brigadier.CustomStringArgumentType.wordWithChars;
import static com.zenith.command.brigadier.ToggleArgumentType.getToggle;
import static com.zenith.command.brigadier.ToggleArgumentType.toggle;

public class AutoChestManagerCommand extends Command {
    private final AutoChestManagerConfig config = FutaPlugin.PLUGIN_CONFIG.autoChest;

    @Override
    public CommandUsage commandUsage() {
        return CommandUsage.builder()
                .name("autochest")
                .category(CommandCategory.MODULE)
                .description("自动箱子管理模块控制命令")
                .usageLines(
                        "setTrash - 设置当前位置为垃圾桶坐标",
                        "setShulker - 设置当前位置为潜影盒坐标",
                        "setEnderChest - 设置当前位置为末影箱坐标",
                        "addItem <物品ID> - 添加需要的物品",
                        "removeItem <物品ID> - 移除需要的物品",
                        "listItems - 列出所有需要的物品",
                        "status - 查看当前配置状态"
                )
                .build();
    }

    @Override
    public LiteralArgumentBuilder<CommandContext> register() {
        return command("autochest")
                .then(argument("toggle", toggle()).executes(c -> {
                    FutaPlugin.PLUGIN_CONFIG.autoChest.enabled = getToggle(c, "toggle");
                    c.getSource().getEmbed()
                            .title("autochest " + toggleStrCaps(FutaPlugin.PLUGIN_CONFIG.autoChest.enabled));
                    MODULE.get(AutoChestManagerModule.class).syncEnabledFromConfig();
                }))
                .then(literal("setTrash").executes(c -> {
                    int x = (int) CACHE.getPlayerCache().getX();
                    int y = (int) CACHE.getPlayerCache().getY();
                    int z = (int) CACHE.getPlayerCache().getZ();
                    config.trashX = x;
                    config.trashY = y;
                    config.trashZ = z;
                    c.getSource().getEmbed().title("设置成功").description("已设置当前位置为垃圾桶: " + x + ", " + y + ", " + z);
                    return OK;
                }))
                .then(literal("setShulker").executes(c -> {
                    int x = (int) CACHE.getPlayerCache().getX();
                    int y = (int) CACHE.getPlayerCache().getY();
                    int z = (int) CACHE.getPlayerCache().getZ();
                    config.shulkerX = x;
                    config.shulkerY = y;
                    config.shulkerZ = z;
                    c.getSource().getEmbed().title("设置成功").description("已设置当前位置为潜影盒: " + x + ", " + y + ", " + z);
                    return OK;
                }))
//                .then(literal("setEnderChest").executes(c -> {
//                    int x = (int) CACHE.getPlayerCache().getX();
//                    int y = (int) CACHE.getPlayerCache().getY();
//                    int z = (int) CACHE.getPlayerCache().getZ();
//                    config.enderChestX = x;
//                    config.enderChestY = y;
//                    config.enderChestZ = z;
//                    c.getSource().getEmbed().title("设置成功").description("已设置当前位置为末影箱: " + x + ", " + y + ", " + z);
//                    return OK;
//                }))
                .then(literal("addItem")
                        .then(argument("item", wordWithChars())
                                .executes(c -> {
                                    String item = getString(c, "item");
                                    config.wantedItems.add(item);
                                    c.getSource().getEmbed().title("添加成功").description("已添加需要的物品: " + item);
                                    return OK;
                                })
                        )
                )
                .then(literal("removeItem")
                        .then(argument("item", wordWithChars())
                                .executes(c -> {
                                    String item = getString(c, "item");
                                    config.wantedItems.remove(item);
                                    c.getSource().getEmbed().title("移除成功").description("已移除需要的物品: " + item);
                                    return OK;
                                })
                        )
                )
                .then(literal("listItems").executes(c -> {
                    var embed = c.getSource().getEmbed().title("需要的物品列表");
                    if (config.wantedItems.isEmpty()) {
                        embed.description("没有配置需要的物品");
                    } else {
                        StringBuilder sb = new StringBuilder();
                        for (String item : config.wantedItems) {
                            sb.append("- ").append(item).append("\n");
                        }
                        embed.description(sb.toString());
                    }
                    return OK;
                }))
                .then(literal("status").executes(c -> {
                    var embed = c.getSource().getEmbed().title("自动箱子管理状态");
                    embed.addField("垃圾桶坐标", config.trashX + ", " + config.trashY + ", " + config.trashZ, true);
                    embed.addField("潜影盒坐标", config.shulkerX + ", " + config.shulkerY + ", " + config.shulkerZ, true);
//                    embed.addField("末影箱坐标", config.enderChestX + ", " + config.enderChestY + ", " + config.enderChestZ, true);
                    embed.addField("需要物品数量", String.valueOf(config.wantedItems.size()), true);
                    return OK;
                }));
    }

    @Override
    public void defaultEmbed(Embed embed) {
        embed.primaryColor()
                .addField("垃圾桶坐标", config.trashX + ", " + config.trashY + ", " + config.trashZ)
                .addField("潜影盒坐标", config.shulkerX + ", " + config.shulkerY + ", " + config.shulkerZ)
//                .addField("末影箱坐标", config.enderChestX + ", " + config.enderChestY + ", " + config.enderChestZ)
                .addField("需要物品数量", String.valueOf(config.wantedItems.size()));
    }
}
