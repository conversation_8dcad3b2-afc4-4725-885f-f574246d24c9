package com.github.mikumiku7.command;

import com.github.mikumiku7.FutaPlugin;
import com.github.mikumiku7.config.ItemSorterConfig;
import com.github.mikumiku7.util.ChestLocation;
import com.github.mikumiku7.util.ContainerSearcher;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.zenith.command.api.Command;
import com.zenith.command.api.CommandCategory;
import com.zenith.command.api.CommandContext;
import com.zenith.command.api.CommandUsage;

import java.util.List;

import static com.github.mikumiku7.FutaPlugin.log;
import static com.mojang.brigadier.arguments.IntegerArgumentType.integer;

public class ItemSorterCommand extends Command {

    private final ItemSorterConfig config = FutaPlugin.PLUGIN_CONFIG.itemSorter;


    @Override
    public CommandUsage commandUsage() {
        return CommandUsage.builder()
                .name("itemsorter")
                .category(CommandCategory.MODULE)
                .description("物品分类模块管理命令")
                .usageLines(
                        "enable",
                        "disable",
                        "status",
                        "addchest <x> <y> <z>",
                        "removechest <index>",
                        "listchests",
                        "addcategory <category> <item1> [item2] ...",
                        "removecategory <category>",
                        "listcategories",
                        "cachestats",
                        "clearcache",
                        "help"
                )
                .aliases("is", "sorter")
                .build();
    }

    @Override
    public LiteralArgumentBuilder<CommandContext> register() {
        return command("itemsorter")
                .executes(c -> {
                    printHelp();
                    return OK;
                })
                .then(literal("enable").executes(c -> {
                    config.enabled = true;
                    c.getSource().getEmbed()
                            .title("物品分类模块已启用")
                            .primaryColor();
                    return OK;
                }))
                .then(literal("disable").executes(c -> {
                    config.enabled = false;
                    c.getSource().getEmbed()
                            .title("物品分类模块已禁用")
                            .primaryColor();
                    return OK;
                }))
                .then(literal("status").executes(c -> {
                    c.getSource().getEmbed()
                            .title("物品分类模块状态")
                            .addField("启用状态", config.enabled ? "已启用" : "已禁用", true)
                            .addField("箱子搜索半径", String.valueOf(config.chestSearchRadius), true)
                            .addField("处理间隔", config.processingIntervalTicks + " ticks", true)
                            .addField("源箱子数量", String.valueOf(config.chestLocations.size()), true)
                            .addField("自定义分类数量", String.valueOf(config.customCategories.size()), true)
                            .addField("智能分类", config.enableSmartClassification ? "已启用" : "已禁用", true)
                            .addField("处理创造物品", config.processCreativeItems ? "已启用" : "已禁用", true)
                            .addField("缺省分类", "物品自己作为分类名", true)
                            .primaryColor();
                    return OK;
                }))
                .then(literal("addchest")
                        .then(argument("x", integer())
                                .then(argument("y", integer())
                                        .then(argument("z", integer()).executes(c -> {
                                            int x = c.getArgument("x", Integer.class);
                                            int y = c.getArgument("y", Integer.class);
                                            int z = c.getArgument("z", Integer.class);

                                            ChestLocation chest = new ChestLocation(x, y, z);
                                            config.chestLocations.add(chest);

                                            c.getSource().getEmbed()
                                                    .title("已添加源箱子坐标")
                                                    .description("坐标: " + chest.toString())
                                                    .primaryColor();
                                            return OK;
                                        })))))
                .then(literal("cachestats").executes(c -> {
                    // 这里需要访问模块实例来获取缓存统计
                    c.getSource().getEmbed()
                            .title("箱子缓存统计")
                            .description("使用 'itemsorter clearcache' 清空缓存")
                            .primaryColor();
                    return OK;
                }))
                .then(literal("clearcache").executes(c -> {
                    // 这里需要访问模块实例来清空缓存
                    c.getSource().getEmbed()
                            .title("箱子缓存已清空")
                            .primaryColor();
                    return OK;
                }))
                .then(literal("help").executes(c -> {
                    printHelp();
                    return OK;
                }));
    }


    private void printHelp() {
        log.info("物品分类模块命令帮助:");
        log.info("使用 'help itemsorter' 查看详细用法");
    }

    // 保留一些辅助方法用于更复杂的命令处理

    private int searchContainers(int radius) {
        log.info("搜索半径 " + radius + " 内的容器...");
        List<ContainerSearcher.ContainerInfo> containers = ContainerSearcher.searchNearbyContainers(radius);

        if (containers.isEmpty()) {
            log.info("没有找到容器");
            return OK;
        }

        log.info("找到 " + containers.size() + " 个容器:");
        for (ContainerSearcher.ContainerInfo container : containers) {
            log.info("  " + container);
        }
        return OK;
    }

}
