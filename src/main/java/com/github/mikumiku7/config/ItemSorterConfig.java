package com.github.mikumiku7.config;

import com.github.mikumiku7.util.ChestInfo;
import com.github.mikumiku7.util.ChestLocation;

import java.util.*;

public class ItemSorterConfig {

    // 是否启用模块
    public boolean enabled = true;

    // 处理间隔时间（tick，60tick = 3秒）
    public int processingIntervalTicks = 60;

    // 要处理的箱子坐标列表
    public List<ChestLocation> chestLocations = new ArrayList<>();


    // 动态箱子搜索半径
    public int chestSearchRadius = 20;

    public int centerX = 0;
    public int centerY = 0;
    public int centerZ = 0;

    // 是否启用智能分类（基于物品属性）
    public boolean enableSmartClassification = true;

    // 箱子缓存更新间隔s
    public int chestCacheUpdateInterval = 600; // 10秒

    // 是否处理创造模式物品
    public boolean processCreativeItems = false;

    // 用户自定义分类规则
    public Map<String, List<String>> customCategories = new HashMap<>();

    public Map<String, ChestInfo> chestCache = new LinkedHashMap<>();


    // 初始化默认配置
    public ItemSorterConfig() {
        initializeDefaultCategories();
    }

    private void initializeDefaultCategories() {
        // === 多物品分类组（只保留包含多个物品的分类）===

        // 土豆类（包含变种）
        customCategories.put("potato", Arrays.asList("potato", "baked_potato", "poisonous_potato"));

        // 苹果类（包含变种）
        customCategories.put("apple", Arrays.asList("apple", "golden_apple", "enchanted_golden_apple"));

        // 牛肉类（生熟一起）
        customCategories.put("beef", Arrays.asList("beef", "cooked_beef"));

        // 猪肉类（生熟一起）
        customCategories.put("porkchop", Arrays.asList("porkchop", "cooked_porkchop"));

        // 鸡肉类（生熟一起）
        customCategories.put("chicken", Arrays.asList("chicken", "cooked_chicken"));

        // 羊肉类（生熟一起）
        customCategories.put("mutton", Arrays.asList("mutton", "cooked_mutton"));

        // 兔肉类（生熟一起）
        customCategories.put("rabbit", Arrays.asList("rabbit", "cooked_rabbit"));

        // 鳕鱼类（生熟一起）
        customCategories.put("cod", Arrays.asList("cod", "cooked_cod"));

        // 鲑鱼类（生熟一起）
        customCategories.put("salmon", Arrays.asList("salmon", "cooked_salmon"));

        // 煤炭类（包含木炭）
        customCategories.put("coal", Arrays.asList("coal", "charcoal"));

        // 铁类（包含相关物品）
        customCategories.put("iron", Arrays.asList("iron_ingot", "raw_iron", "iron_nugget"));

        // 金类（包含相关物品）
        customCategories.put("gold", Arrays.asList("gold_ingot", "raw_gold", "gold_nugget"));

        // 铜类（包含相关物品）
        customCategories.put("copper", Arrays.asList("copper_ingot", "raw_copper"));

        // 下界合金类（包含相关物品）
        customCategories.put("netherite", Arrays.asList("netherite_ingot", "netherite_scrap", "ancient_debris"));

        // 石头类（包含圆石）
        customCategories.put("stone", Arrays.asList("stone", "cobblestone"));

        // 泥土类（包含变种）
        customCategories.put("dirt", Arrays.asList("dirt", "coarse_dirt", "grass_block"));

        // 沙子类（包含红沙）
        customCategories.put("sand", Arrays.asList("sand", "red_sand"));

        // 圆石类（包含苔石圆石）
        customCategories.put("cobblestone", Arrays.asList("cobblestone", "mossy_cobblestone"));

        // 原木类（包含去皮版本）
        customCategories.put("oak_log", Arrays.asList("oak_log", "stripped_oak_log"));
        customCategories.put("spruce_log", Arrays.asList("spruce_log", "stripped_spruce_log"));
        customCategories.put("birch_log", Arrays.asList("birch_log", "stripped_birch_log"));
        customCategories.put("jungle_log", Arrays.asList("jungle_log", "stripped_jungle_log"));
        customCategories.put("acacia_log", Arrays.asList("acacia_log", "stripped_acacia_log"));
        customCategories.put("dark_oak_log", Arrays.asList("dark_oak_log", "stripped_dark_oak_log"));

        // === 颜色变种合并（同种物品不同颜色放一起）===

        // 羊毛（所有颜色）
        customCategories.put("wool", Arrays.asList(
                "white_wool", "orange_wool", "magenta_wool", "light_blue_wool", "yellow_wool", "lime_wool",
                "pink_wool", "gray_wool", "light_gray_wool", "cyan_wool", "purple_wool", "blue_wool",
                "brown_wool", "green_wool", "red_wool", "black_wool"
        ));

        // 混凝土（所有颜色）
        customCategories.put("concrete", Arrays.asList(
                "white_concrete", "orange_concrete", "magenta_concrete", "light_blue_concrete", "yellow_concrete", "lime_concrete",
                "pink_concrete", "gray_concrete", "light_gray_concrete", "cyan_concrete", "purple_concrete", "blue_concrete",
                "brown_concrete", "green_concrete", "red_concrete", "black_concrete"
        ));

        // 陶瓦（所有颜色）
        customCategories.put("terracotta", Arrays.asList(
                "terracotta", "white_terracotta", "orange_terracotta", "magenta_terracotta", "light_blue_terracotta", "yellow_terracotta", "lime_terracotta",
                "pink_terracotta", "gray_terracotta", "light_gray_terracotta", "cyan_terracotta", "purple_terracotta", "blue_terracotta",
                "brown_terracotta", "green_terracotta", "red_terracotta", "black_terracotta"
        ));

        // 玻璃（所有颜色）
        customCategories.put("glass", Arrays.asList(
                "glass", "tinted_glass", "white_stained_glass", "orange_stained_glass", "magenta_stained_glass", "light_blue_stained_glass",
                "yellow_stained_glass", "lime_stained_glass", "pink_stained_glass", "gray_stained_glass", "light_gray_stained_glass",
                "cyan_stained_glass", "purple_stained_glass", "blue_stained_glass", "brown_stained_glass", "green_stained_glass", "red_stained_glass", "black_stained_glass"
        ));

        // 地毯（所有颜色）
        customCategories.put("carpet", Arrays.asList(
                "white_carpet", "orange_carpet", "magenta_carpet", "light_blue_carpet", "yellow_carpet", "lime_carpet",
                "pink_carpet", "gray_carpet", "light_gray_carpet", "cyan_carpet", "purple_carpet", "blue_carpet",
                "brown_carpet", "green_carpet", "red_carpet", "black_carpet"
        ));

        // 床（所有颜色）
        customCategories.put("bed", Arrays.asList(
                "white_bed", "orange_bed", "magenta_bed", "light_blue_bed", "yellow_bed", "lime_bed",
                "pink_bed", "gray_bed", "light_gray_bed", "cyan_bed", "purple_bed", "blue_bed",
                "brown_bed", "green_bed", "red_bed", "black_bed"
        ));

        // === 特殊稀有物品合并（生存模式可获得）===

        // 音乐唱片（生存可获得）
        customCategories.put("music_discs", Arrays.asList(
                "music_disc_13", "music_disc_cat", "music_disc_blocks", "music_disc_chirp",
                "music_disc_far", "music_disc_mall", "music_disc_mellohi", "music_disc_stal", "music_disc_strd", "music_disc_ward",
                "music_disc_11", "music_disc_wait", "music_disc_otherside", "music_disc_5", "music_disc_pigstep"
        ));

        // 陶片（生存可获得）
        customCategories.put("pottery_sherds", Arrays.asList(
                "angler_pottery_sherd", "archer_pottery_sherd", "arms_up_pottery_sherd", "blade_pottery_sherd", "brewer_pottery_sherd",
                "burn_pottery_sherd", "danger_pottery_sherd", "explorer_pottery_sherd", "friend_pottery_sherd",
                "heart_pottery_sherd", "heartbreak_pottery_sherd", "howl_pottery_sherd", "miner_pottery_sherd",
                "mourner_pottery_sherd", "plenty_pottery_sherd", "prize_pottery_sherd", "scrape_pottery_sherd", "sheaf_pottery_sherd",
                "shelter_pottery_sherd", "skull_pottery_sherd", "snort_pottery_sherd"
        ));

        // 超稀有独特物品（生存可获得）
        customCategories.put("ultra_rare", Arrays.asList(
                "elytra", "totem_of_undying", "nether_star", "beacon", "conduit", "dragon_head"
        ));

        // 附魔书
        customCategories.put("enchanted_books", Arrays.asList("enchanted_book"));

        // 药水
        customCategories.put("potions", Arrays.asList("potion", "splash_potion", "lingering_potion"));

        // 垃圾物品
        customCategories.put("trash", Arrays.asList(
                "rotten_flesh", "spider_eye", "fermented_spider_eye", "bone", "string", "feather",
                "leather", "rabbit_hide", "slime_ball", "phantom_membrane", "stick"
        ));
    }


}
