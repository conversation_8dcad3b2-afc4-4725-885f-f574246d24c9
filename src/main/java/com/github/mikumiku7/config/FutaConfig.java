package com.github.mikumiku7.config;

/**
 * Example configuration POJO.
 * <p>
 * Configurations are saved and loaded to JSON files
 * <p>
 * All fields should be public and mutable.
 * <p>
 * Fields to static inner classes generate nested JSON objects.
 */
public class FutaConfig {
    // 是否启用插件
    public boolean enabled = true;

    public AutoChestManagerConfig autoChest = new AutoChestManagerConfig();

    public ItemSorterConfig itemSorter = new ItemSorterConfig();


}
